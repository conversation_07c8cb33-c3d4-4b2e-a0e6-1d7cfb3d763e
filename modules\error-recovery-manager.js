/**
 * MDAC AI智能分析工具 - 增强的错误处理和恢复机制
 * 智能错误分类、诊断和恢复建议系统
 */

class ErrorRecoveryManager {
    constructor() {
        // 错误分类定义
        this.errorCategories = {
            // AI相关错误
            AI_ERROR: {
                name: 'AI处理错误',
                icon: '🤖',
                color: '#ff6b6b',
                priority: 'high'
            },
            
            // 网络连接错误
            NETWORK_ERROR: {
                name: '网络连接错误',
                icon: '🌐',
                color: '#ffa726',
                priority: 'high'
            },
            
            // 表单字段错误
            FORM_ERROR: {
                name: '表单字段错误',
                icon: '📝',
                color: '#42a5f5',
                priority: 'medium'
            },
            
            // 数据验证错误
            VALIDATION_ERROR: {
                name: '数据验证错误',
                icon: '⚠️',
                color: '#ff9800',
                priority: 'medium'
            },
            
            // 权限错误
            PERMISSION_ERROR: {
                name: '权限错误',
                icon: '🔒',
                color: '#e53e3e',
                priority: 'high'
            },
            
            // 配置错误
            CONFIG_ERROR: {
                name: '配置错误',
                icon: '⚙️',
                color: '#9c27b0',
                priority: 'medium'
            },
            
            // 用户操作错误
            USER_ERROR: {
                name: '用户操作错误',
                icon: '👤',
                color: '#66bb6a',
                priority: 'low'
            },
            
            // 未知错误
            UNKNOWN_ERROR: {
                name: '未知错误',
                icon: '❓',
                color: '#78909c',
                priority: 'medium'
            }
        };
        
        // 错误模式匹配规则
        this.errorPatterns = {
            // AI相关错误模式
            AI_ERROR: [
                /api.*key.*invalid/i,
                /gemini.*error/i,
                /ai.*service.*unavailable/i,
                /quota.*exceeded/i,
                /rate.*limit/i,
                /model.*not.*found/i,
                /authentication.*failed/i
            ],
            
            // 网络错误模式
            NETWORK_ERROR: [
                /network.*error/i,
                /connection.*failed/i,
                /timeout/i,
                /fetch.*failed/i,
                /cors.*error/i,
                /net::/i,
                /offline/i
            ],
            
            // 表单错误模式
            FORM_ERROR: [
                /field.*not.*found/i,
                /element.*not.*found/i,
                /form.*not.*found/i,
                /selector.*invalid/i,
                /cannot.*fill.*field/i,
                /field.*disabled/i
            ],
            
            // 验证错误模式
            VALIDATION_ERROR: [
                /validation.*failed/i,
                /invalid.*format/i,
                /required.*field/i,
                /missing.*data/i,
                /format.*error/i,
                /invalid.*email/i,
                /invalid.*date/i
            ],
            
            // 权限错误模式
            PERMISSION_ERROR: [
                /permission.*denied/i,
                /access.*denied/i,
                /unauthorized/i,
                /forbidden/i,
                /blocked.*by.*browser/i
            ],
            
            // 配置错误模式
            CONFIG_ERROR: [
                /config.*error/i,
                /setting.*invalid/i,
                /configuration.*missing/i,
                /api.*key.*missing/i
            ]
        };
        
        // 解决方案模板
        this.solutionTemplates = {
            AI_ERROR: {
                'api.*key.*invalid': {
                    title: 'API密钥无效',
                    description: 'Gemini API密钥配置错误或已过期',
                    solutions: [
                        '检查API密钥是否正确配置',
                        '确认API密钥是否有效且未过期',
                        '重新生成新的API密钥',
                        '联系管理员获取有效密钥'
                    ],
                    autoFix: 'openSettings'
                },
                'quota.*exceeded': {
                    title: 'API配额超限',
                    description: 'Gemini API使用配额已达到限制',
                    solutions: [
                        '等待配额重置（通常在下个计费周期）',
                        '升级API计划以获得更多配额',
                        '优化API调用频率',
                        '使用备用API密钥'
                    ],
                    autoFix: 'showQuotaInfo'
                }
            },
            
            NETWORK_ERROR: {
                'connection.*failed': {
                    title: '网络连接失败',
                    description: '无法连接到服务器或网络不稳定',
                    solutions: [
                        '检查网络连接是否正常',
                        '尝试刷新页面重新连接',
                        '检查防火墙或代理设置',
                        '稍后重试操作'
                    ],
                    autoFix: 'retryConnection'
                },
                'timeout': {
                    title: '请求超时',
                    description: '网络请求响应时间过长',
                    solutions: [
                        '检查网络速度是否正常',
                        '减少请求数据量',
                        '增加超时时间设置',
                        '重试操作'
                    ],
                    autoFix: 'retryWithTimeout'
                }
            },
            
            FORM_ERROR: {
                'field.*not.*found': {
                    title: '表单字段未找到',
                    description: '目标表单字段在页面中不存在',
                    solutions: [
                        '确认当前页面是MDAC表单页面',
                        '等待页面完全加载后重试',
                        '检查页面是否有更新',
                        '使用手动填充模式'
                    ],
                    autoFix: 'redetectFields'
                },
                'form.*not.*found': {
                    title: '表单未找到',
                    description: '页面中没有找到目标表单',
                    solutions: [
                        '确认当前在正确的MDAC页面',
                        '刷新页面重新加载',
                        '检查页面URL是否正确',
                        '联系技术支持'
                    ],
                    autoFix: 'navigateToForm'
                }
            },
            
            VALIDATION_ERROR: {
                'invalid.*format': {
                    title: '数据格式错误',
                    description: '输入数据格式不符合要求',
                    solutions: [
                        '检查数据格式是否正确',
                        '使用标准格式（如日期：DD/MM/YYYY）',
                        '清理数据中的特殊字符',
                        '重新解析原始数据'
                    ],
                    autoFix: 'formatData'
                },
                'required.*field': {
                    title: '必填字段缺失',
                    description: '存在未填写的必填字段',
                    solutions: [
                        '补充缺失的必填字段信息',
                        '检查原始数据是否完整',
                        '使用数据预览功能手动填写',
                        '重新解析更完整的数据'
                    ],
                    autoFix: 'showMissingFields'
                }
            }
        };
        
        // 错误历史记录
        this.errorHistory = [];
        this.maxHistorySize = 50;
        
        // 自动恢复配置
        this.autoRecoveryConfig = {
            maxRetries: 3,
            retryDelay: 1000,
            enableAutoFix: true
        };
        
        // 统计信息
        this.stats = {
            totalErrors: 0,
            resolvedErrors: 0,
            autoFixedErrors: 0,
            userResolvedErrors: 0
        };
    }
    
    /**
     * 处理错误
     * @param {Error|string} error 错误对象或错误消息
     * @param {Object} context 错误上下文信息
     * @returns {Object} 错误处理结果
     */
    async handleError(error, context = {}) {
        console.log('🚨 错误处理开始:', error);
        
        // 标准化错误信息
        const errorInfo = this.normalizeError(error, context);
        
        // 分类错误
        const category = this.classifyError(errorInfo);
        
        // 生成解决方案
        const solutions = this.generateSolutions(errorInfo, category);
        
        // 记录错误
        this.recordError(errorInfo, category, solutions);
        
        // 更新统计
        this.stats.totalErrors++;
        
        // 尝试自动修复
        let autoFixResult = null;
        if (this.autoRecoveryConfig.enableAutoFix && solutions.autoFix) {
            autoFixResult = await this.attemptAutoFix(solutions.autoFix, errorInfo, context);
        }
        
        // 显示错误界面
        this.showErrorInterface(errorInfo, category, solutions, autoFixResult);
        
        const result = {
            errorInfo,
            category,
            solutions,
            autoFixResult,
            canRecover: solutions.solutions.length > 0,
            timestamp: Date.now()
        };
        
        console.log('📊 错误处理完成:', result);
        return result;
    }
    
    /**
     * 标准化错误信息
     */
    normalizeError(error, context) {
        let message, stack, code;
        
        if (error instanceof Error) {
            message = error.message;
            stack = error.stack;
            code = error.code;
        } else if (typeof error === 'string') {
            message = error;
            stack = new Error().stack;
        } else {
            message = JSON.stringify(error);
            stack = new Error().stack;
        }
        
        return {
            message,
            stack,
            code,
            context: {
                url: window.location.href,
                userAgent: navigator.userAgent,
                timestamp: Date.now(),
                ...context
            }
        };
    }
    
    /**
     * 分类错误
     */
    classifyError(errorInfo) {
        const message = errorInfo.message.toLowerCase();
        
        // 遍历错误模式进行匹配
        for (const [categoryKey, patterns] of Object.entries(this.errorPatterns)) {
            for (const pattern of patterns) {
                if (pattern.test(message)) {
                    return {
                        key: categoryKey,
                        ...this.errorCategories[categoryKey]
                    };
                }
            }
        }
        
        // 默认为未知错误
        return {
            key: 'UNKNOWN_ERROR',
            ...this.errorCategories.UNKNOWN_ERROR
        };
    }
    
    /**
     * 生成解决方案
     */
    generateSolutions(errorInfo, category) {
        const message = errorInfo.message.toLowerCase();
        const categoryTemplates = this.solutionTemplates[category.key] || {};
        
        // 查找匹配的解决方案模板
        for (const [pattern, template] of Object.entries(categoryTemplates)) {
            if (new RegExp(pattern, 'i').test(message)) {
                return {
                    ...template,
                    category: category.key,
                    confidence: 0.9
                };
            }
        }
        
        // 生成通用解决方案
        return this.generateGenericSolutions(category);
    }
    
    /**
     * 生成通用解决方案
     */
    generateGenericSolutions(category) {
        const genericSolutions = {
            AI_ERROR: {
                title: 'AI服务错误',
                description: '人工智能服务遇到问题',
                solutions: [
                    '检查AI服务配置',
                    '重试AI请求',
                    '联系技术支持',
                    '使用手动模式'
                ],
                autoFix: 'retryAI'
            },
            
            NETWORK_ERROR: {
                title: '网络连接问题',
                description: '网络连接出现异常',
                solutions: [
                    '检查网络连接',
                    '刷新页面重试',
                    '检查代理设置',
                    '稍后重试'
                ],
                autoFix: 'retryConnection'
            },
            
            FORM_ERROR: {
                title: '表单操作错误',
                description: '表单字段操作失败',
                solutions: [
                    '刷新页面重新加载',
                    '检查页面是否正确',
                    '使用手动填充',
                    '重新检测字段'
                ],
                autoFix: 'redetectFields'
            },
            
            VALIDATION_ERROR: {
                title: '数据验证失败',
                description: '输入数据不符合要求',
                solutions: [
                    '检查数据格式',
                    '补充缺失信息',
                    '使用数据预览功能',
                    '重新解析数据'
                ],
                autoFix: 'showDataPreview'
            }
        };
        
        return genericSolutions[category.key] || {
            title: '未知错误',
            description: '遇到未知问题',
            solutions: [
                '刷新页面重试',
                '检查浏览器控制台',
                '联系技术支持',
                '重启扩展'
            ],
            autoFix: 'restart'
        };
    }
    
    /**
     * 记录错误
     */
    recordError(errorInfo, category, solutions) {
        const errorRecord = {
            id: Date.now().toString(),
            timestamp: Date.now(),
            errorInfo,
            category,
            solutions,
            resolved: false,
            autoFixed: false
        };
        
        this.errorHistory.unshift(errorRecord);
        
        // 限制历史记录大小
        if (this.errorHistory.length > this.maxHistorySize) {
            this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
        }
        
        // 保存到本地存储
        this.saveErrorHistory();
    }
    
    /**
     * 保存错误历史到本地存储
     */
    async saveErrorHistory() {
        try {
            await chrome.storage.local.set({
                errorHistory: this.errorHistory.slice(0, 20), // 只保存最近20条
                errorStats: this.stats
            });
        } catch (error) {
            console.error('保存错误历史失败:', error);
        }
    }
    
    /**
     * 加载错误历史
     */
    async loadErrorHistory() {
        try {
            const result = await chrome.storage.local.get(['errorHistory', 'errorStats']);
            if (result.errorHistory) {
                this.errorHistory = result.errorHistory;
            }
            if (result.errorStats) {
                this.stats = { ...this.stats, ...result.errorStats };
            }
        } catch (error) {
            console.error('加载错误历史失败:', error);
        }
    }

    /**
     * 尝试自动修复
     */
    async attemptAutoFix(autoFixType, errorInfo, context) {
        console.log(`🔧 尝试自动修复: ${autoFixType}`);

        try {
            let result = false;

            switch (autoFixType) {
                case 'retryConnection':
                    result = await this.retryConnection(context);
                    break;

                case 'retryAI':
                    result = await this.retryAIRequest(context);
                    break;

                case 'redetectFields':
                    result = await this.redetectFormFields(context);
                    break;

                case 'formatData':
                    result = await this.formatData(context);
                    break;

                case 'showDataPreview':
                    result = await this.showDataPreview(context);
                    break;

                case 'openSettings':
                    result = await this.openSettings(context);
                    break;

                case 'restart':
                    result = await this.restartExtension(context);
                    break;

                default:
                    console.log(`未知的自动修复类型: ${autoFixType}`);
                    result = false;
            }

            if (result) {
                this.stats.autoFixedErrors++;
                console.log('✅ 自动修复成功');
            } else {
                console.log('❌ 自动修复失败');
            }

            return {
                success: result,
                type: autoFixType,
                timestamp: Date.now()
            };

        } catch (error) {
            console.error('自动修复过程中出错:', error);
            return {
                success: false,
                type: autoFixType,
                error: error.message,
                timestamp: Date.now()
            };
        }
    }

    /**
     * 重试网络连接
     */
    async retryConnection(context) {
        try {
            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, this.autoRecoveryConfig.retryDelay));

            // 尝试发送测试请求
            const response = await fetch(window.location.href, { method: 'HEAD' });
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    /**
     * 重试AI请求
     */
    async retryAIRequest(context) {
        try {
            if (context.retryFunction && typeof context.retryFunction === 'function') {
                return await context.retryFunction();
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    /**
     * 重新检测表单字段
     */
    async redetectFormFields(context) {
        try {
            // 通知content script重新检测字段
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tabs[0]) {
                await chrome.tabs.sendMessage(tabs[0].id, {
                    action: 'redetectFields'
                });
                return true;
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    /**
     * 格式化数据
     */
    async formatData(context) {
        try {
            if (context.data && context.formatFunction) {
                context.formatFunction(context.data);
                return true;
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    /**
     * 显示数据预览
     */
    async showDataPreview(context) {
        try {
            if (window.dataPreviewManager && context.data) {
                window.dataPreviewManager.showPreview(context.data);
                return true;
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    /**
     * 打开设置页面
     */
    async openSettings(context) {
        try {
            chrome.runtime.openOptionsPage();
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * 重启扩展
     */
    async restartExtension(context) {
        try {
            chrome.runtime.reload();
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * 显示错误界面
     */
    showErrorInterface(errorInfo, category, solutions, autoFixResult) {
        // 移除现有的错误界面
        this.removeExistingErrorInterface();

        const errorModal = document.createElement('div');
        errorModal.id = 'mdac-error-recovery-modal';
        errorModal.className = 'mdac-error-modal';

        const hasAutoFix = autoFixResult && autoFixResult.success;
        const canRetry = solutions.autoFix && !hasAutoFix;

        errorModal.innerHTML = `
            <div class="error-overlay" onclick="window.errorRecoveryManager.closeErrorInterface()"></div>
            <div class="error-container">
                <div class="error-header" style="background: ${category.color}">
                    <div class="error-title">
                        <span class="error-icon">${category.icon}</span>
                        <h2>${category.name}</h2>
                    </div>
                    <div class="error-priority priority-${category.priority}">
                        ${category.priority.toUpperCase()}
                    </div>
                </div>

                <div class="error-content">
                    <div class="error-summary">
                        <h3>${solutions.title}</h3>
                        <p class="error-description">${solutions.description}</p>
                        <div class="error-details">
                            <strong>错误信息:</strong> ${errorInfo.message}
                        </div>
                    </div>

                    ${hasAutoFix ? `
                        <div class="auto-fix-success">
                            <div class="success-icon">✅</div>
                            <div class="success-text">
                                <strong>自动修复成功</strong>
                                <p>系统已自动解决此问题，您可以继续操作。</p>
                            </div>
                        </div>
                    ` : ''}

                    <div class="solutions-section">
                        <h4>解决方案</h4>
                        <div class="solutions-list">
                            ${solutions.solutions.map((solution, index) => `
                                <div class="solution-item">
                                    <span class="solution-number">${index + 1}</span>
                                    <span class="solution-text">${solution}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="error-actions">
                        ${canRetry ? `
                            <button class="error-btn primary" onclick="window.errorRecoveryManager.retryAutoFix('${solutions.autoFix}', '${errorInfo.message}')">
                                <span class="btn-icon">🔧</span>
                                <span class="btn-text">自动修复</span>
                            </button>
                        ` : ''}

                        <button class="error-btn secondary" onclick="window.errorRecoveryManager.showErrorHistory()">
                            <span class="btn-icon">📋</span>
                            <span class="btn-text">错误历史</span>
                        </button>

                        <button class="error-btn secondary" onclick="window.errorRecoveryManager.reportError('${errorInfo.message}')">
                            <span class="btn-icon">📧</span>
                            <span class="btn-text">报告问题</span>
                        </button>

                        <button class="error-btn success" onclick="window.errorRecoveryManager.markAsResolved('${Date.now()}')">
                            <span class="btn-icon">✅</span>
                            <span class="btn-text">已解决</span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        this.addErrorStyles();

        // 插入到页面
        document.body.appendChild(errorModal);

        // 暴露到全局
        window.errorRecoveryManager = this;

        console.log('🖥️ 错误界面已显示');
    }

    /**
     * 移除现有的错误界面
     */
    removeExistingErrorInterface() {
        const existingModal = document.getElementById('mdac-error-recovery-modal');
        if (existingModal) {
            existingModal.remove();
        }

        const existingHistory = document.querySelector('.error-history-modal');
        if (existingHistory) {
            existingHistory.remove();
        }
    }

    /**
     * 关闭错误界面
     */
    closeErrorInterface() {
        this.removeExistingErrorInterface();

        // 清理全局引用
        if (window.errorRecoveryManager === this) {
            delete window.errorRecoveryManager;
        }

        console.log('❌ 错误界面已关闭');
    }

    /**
     * 重试自动修复
     */
    async retryAutoFix(autoFixType, errorMessage) {
        console.log(`🔄 重试自动修复: ${autoFixType}`);

        // 显示加载状态
        const retryBtn = document.querySelector('.error-btn.primary');
        if (retryBtn) {
            retryBtn.disabled = true;
            retryBtn.innerHTML = '<span class="btn-icon">⏳</span><span class="btn-text">修复中...</span>';
        }

        try {
            const result = await this.attemptAutoFix(autoFixType, { message: errorMessage }, {});

            if (result.success) {
                // 显示成功状态
                if (retryBtn) {
                    retryBtn.innerHTML = '<span class="btn-icon">✅</span><span class="btn-text">修复成功</span>';
                    retryBtn.className = 'error-btn success';
                }

                // 3秒后关闭界面
                setTimeout(() => {
                    this.closeErrorInterface();
                }, 3000);

            } else {
                // 显示失败状态
                if (retryBtn) {
                    retryBtn.innerHTML = '<span class="btn-icon">❌</span><span class="btn-text">修复失败</span>';
                    retryBtn.className = 'error-btn danger';
                    retryBtn.disabled = false;
                }
            }

        } catch (error) {
            console.error('重试自动修复失败:', error);
            if (retryBtn) {
                retryBtn.innerHTML = '<span class="btn-icon">❌</span><span class="btn-text">修复失败</span>';
                retryBtn.className = 'error-btn danger';
                retryBtn.disabled = false;
            }
        }
    }

    /**
     * 显示错误历史
     */
    showErrorHistory() {
        const historyModal = document.createElement('div');
        historyModal.className = 'error-history-modal';
        historyModal.innerHTML = `
            <div class="history-overlay" onclick="this.parentElement.remove()"></div>
            <div class="history-container">
                <div class="history-header">
                    <h3>错误历史记录</h3>
                    <button class="history-close" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
                <div class="history-content">
                    <div class="history-stats">
                        <div class="stat-item">
                            <span class="stat-label">总错误数</span>
                            <span class="stat-value">${this.stats.totalErrors}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">已解决</span>
                            <span class="stat-value">${this.stats.resolvedErrors}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">自动修复</span>
                            <span class="stat-value">${this.stats.autoFixedErrors}</span>
                        </div>
                    </div>
                    <div class="history-list">
                        ${this.errorHistory.length > 0 ? this.errorHistory.map(error => `
                            <div class="history-item ${error.resolved ? 'resolved' : ''}">
                                <div class="history-item-header">
                                    <span class="history-category" style="color: ${this.errorCategories[error.category.key]?.color || '#666'}">
                                        ${error.category.icon} ${error.category.name}
                                    </span>
                                    <span class="history-time">${new Date(error.timestamp).toLocaleString()}</span>
                                </div>
                                <div class="history-item-content">
                                    <div class="history-message">${error.errorInfo.message}</div>
                                    <div class="history-status">
                                        ${error.resolved ? '✅ 已解决' : error.autoFixed ? '🔧 自动修复' : '⏳ 待解决'}
                                    </div>
                                </div>
                            </div>
                        `).join('') : '<div class="no-history">暂无错误记录</div>'}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(historyModal);
    }

    /**
     * 报告错误
     */
    reportError(errorMessage) {
        const reportData = {
            error: errorMessage,
            url: window.location.href,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString(),
            stats: this.stats
        };

        // 复制报告数据到剪贴板
        navigator.clipboard.writeText(JSON.stringify(reportData, null, 2)).then(() => {
            alert('错误报告已复制到剪贴板，请发送给技术支持团队。');
        }).catch(() => {
            // 降级方案：显示报告数据
            const reportWindow = window.open('', '_blank');
            reportWindow.document.write(`
                <html>
                    <head><title>错误报告</title></head>
                    <body>
                        <h2>MDAC AI 错误报告</h2>
                        <pre>${JSON.stringify(reportData, null, 2)}</pre>
                        <p>请将此信息发送给技术支持团队。</p>
                    </body>
                </html>
            `);
        });
    }

    /**
     * 标记为已解决
     */
    markAsResolved(errorId) {
        // 更新错误历史
        const errorIndex = this.errorHistory.findIndex(error => error.id === errorId);
        if (errorIndex !== -1) {
            this.errorHistory[errorIndex].resolved = true;
            this.stats.userResolvedErrors++;
            this.stats.resolvedErrors++;
            this.saveErrorHistory();
        }

        // 关闭错误界面
        this.closeErrorInterface();

        console.log('✅ 错误已标记为解决');
    }

    /**
     * 获取错误统计
     */
    getErrorStats() {
        return {
            ...this.stats,
            successRate: this.stats.totalErrors > 0
                ? ((this.stats.resolvedErrors / this.stats.totalErrors) * 100).toFixed(2)
                : 100,
            autoFixRate: this.stats.totalErrors > 0
                ? ((this.stats.autoFixedErrors / this.stats.totalErrors) * 100).toFixed(2)
                : 0
        };
    }

    /**
     * 清除错误历史
     */
    async clearErrorHistory() {
        this.errorHistory = [];
        this.stats = {
            totalErrors: 0,
            resolvedErrors: 0,
            autoFixedErrors: 0,
            userResolvedErrors: 0
        };

        await this.saveErrorHistory();
        console.log('🗑️ 错误历史已清除');
    }

    /**
     * 添加错误界面样式
     */
    addErrorStyles() {
        if (document.getElementById('mdac-error-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'mdac-error-styles';
        styles.textContent = `
            .mdac-error-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10002;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .error-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                backdrop-filter: blur(3px);
            }

            .error-container {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 90%;
                max-width: 600px;
                max-height: 80%;
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
                overflow: hidden;
                animation: errorSlideIn 0.3s ease-out;
            }

            @keyframes errorSlideIn {
                from {
                    opacity: 0;
                    transform: translate(-50%, -60%);
                }
                to {
                    opacity: 1;
                    transform: translate(-50%, -50%);
                }
            }

            .error-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 20px 24px;
                color: white;
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            }

            .error-title {
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .error-icon {
                font-size: 24px;
            }

            .error-title h2 {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
            }

            .error-priority {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 11px;
                font-weight: 600;
                text-transform: uppercase;
            }

            .priority-high {
                background: rgba(255, 255, 255, 0.3);
                color: white;
            }

            .priority-medium {
                background: rgba(255, 255, 255, 0.2);
                color: white;
            }

            .priority-low {
                background: rgba(255, 255, 255, 0.1);
                color: white;
            }

            .error-content {
                padding: 24px;
                max-height: 60vh;
                overflow-y: auto;
            }

            .error-summary h3 {
                margin: 0 0 8px 0;
                font-size: 16px;
                color: #333;
            }

            .error-description {
                margin: 0 0 16px 0;
                color: #666;
                line-height: 1.5;
            }

            .error-details {
                padding: 12px;
                background: #f8f9fa;
                border-radius: 6px;
                border-left: 4px solid #ff6b6b;
                margin-bottom: 20px;
                font-size: 14px;
                color: #495057;
            }

            .auto-fix-success {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 16px;
                background: #d4edda;
                border: 1px solid #c3e6cb;
                border-radius: 8px;
                margin-bottom: 20px;
            }

            .success-icon {
                font-size: 24px;
            }

            .success-text strong {
                display: block;
                color: #155724;
                margin-bottom: 4px;
            }

            .success-text p {
                margin: 0;
                color: #155724;
                font-size: 14px;
            }

            .solutions-section h4 {
                margin: 0 0 12px 0;
                font-size: 14px;
                color: #333;
                font-weight: 600;
            }

            .solutions-list {
                display: flex;
                flex-direction: column;
                gap: 8px;
                margin-bottom: 24px;
            }

            .solution-item {
                display: flex;
                align-items: flex-start;
                gap: 12px;
                padding: 8px 0;
            }

            .solution-number {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 20px;
                background: #007bff;
                color: white;
                border-radius: 50%;
                font-size: 12px;
                font-weight: 600;
                flex-shrink: 0;
            }

            .solution-text {
                flex: 1;
                font-size: 14px;
                color: #495057;
                line-height: 1.4;
            }

            .error-actions {
                display: flex;
                gap: 8px;
                flex-wrap: wrap;
            }

            .error-btn {
                display: flex;
                align-items: center;
                gap: 6px;
                padding: 8px 12px;
                border: none;
                border-radius: 6px;
                font-size: 13px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                text-decoration: none;
            }

            .error-btn.primary {
                background: #007bff;
                color: white;
            }

            .error-btn.primary:hover {
                background: #0056b3;
                transform: translateY(-1px);
            }

            .error-btn.secondary {
                background: #6c757d;
                color: white;
            }

            .error-btn.secondary:hover {
                background: #545b62;
            }

            .error-btn.success {
                background: #28a745;
                color: white;
            }

            .error-btn.success:hover {
                background: #1e7e34;
            }

            .error-btn.danger {
                background: #dc3545;
                color: white;
            }

            .error-btn.danger:hover {
                background: #c82333;
            }

            .error-btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }

            .btn-icon {
                font-size: 14px;
            }

            .error-history-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10003;
                font-family: inherit;
            }

            .history-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.4);
            }

            .history-container {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 90%;
                max-width: 700px;
                max-height: 80%;
                background: white;
                border-radius: 8px;
                overflow: hidden;
            }

            .history-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 16px 20px;
                background: #f8f9fa;
                border-bottom: 1px solid #e9ecef;
            }

            .history-header h3 {
                margin: 0;
                font-size: 16px;
                color: #333;
            }

            .history-close {
                background: none;
                border: none;
                font-size: 20px;
                cursor: pointer;
                color: #666;
                padding: 0;
                width: 24px;
                height: 24px;
            }

            .history-content {
                padding: 20px;
                max-height: 60vh;
                overflow-y: auto;
            }

            .history-stats {
                display: flex;
                gap: 20px;
                margin-bottom: 20px;
                padding: 16px;
                background: #f8f9fa;
                border-radius: 6px;
            }

            .stat-item {
                text-align: center;
            }

            .stat-label {
                display: block;
                font-size: 12px;
                color: #6c757d;
                margin-bottom: 4px;
            }

            .stat-value {
                display: block;
                font-size: 18px;
                font-weight: 600;
                color: #495057;
            }

            .history-list {
                display: flex;
                flex-direction: column;
                gap: 12px;
            }

            .history-item {
                padding: 12px;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                background: white;
            }

            .history-item.resolved {
                background: #f8fff9;
                border-color: #c3e6cb;
            }

            .history-item-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;
            }

            .history-category {
                font-size: 14px;
                font-weight: 500;
            }

            .history-time {
                font-size: 12px;
                color: #6c757d;
            }

            .history-message {
                font-size: 13px;
                color: #495057;
                margin-bottom: 4px;
            }

            .history-status {
                font-size: 12px;
                font-weight: 500;
            }

            .no-history {
                text-align: center;
                padding: 40px;
                color: #6c757d;
                font-style: italic;
            }
        `;

        document.head.appendChild(styles);
    }
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ErrorRecoveryManager;
} else {
    window.ErrorRecoveryManager = ErrorRecoveryManager;
}
