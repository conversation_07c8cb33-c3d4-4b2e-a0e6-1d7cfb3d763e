/**
 * MDAC AI智能分析工具 - AI解析结果置信度评估系统
 * 评估AI解析结果的可靠性，标识可能存在问题的字段
 */

class ConfidenceEvaluator {
    constructor() {
        // 字段类型定义和验证规则
        this.fieldTypes = {
            // 个人信息字段
            name: {
                type: 'text',
                required: true,
                patterns: [/^[a-zA-Z\s\u4e00-\u9fff]{2,50}$/],
                minLength: 2,
                maxLength: 50,
                weight: 10
            },
            passportNo: {
                type: 'text',
                required: true,
                patterns: [/^[A-Z0-9]{6,12}$/],
                minLength: 6,
                maxLength: 12,
                weight: 10
            },
            dateOfBirth: {
                type: 'date',
                required: true,
                patterns: [/^\d{2}\/\d{2}\/\d{4}$/],
                dateRange: { min: '01/01/1900', max: new Date().toLocaleDateString('en-GB') },
                weight: 9
            },
            nationality: {
                type: 'select',
                required: true,
                validValues: ['CHN', 'USA', 'SGP', 'MYS', 'THA', 'IDN', 'JPN', 'KOR', 'IND', 'GBR'],
                weight: 8
            },
            sex: {
                type: 'select',
                required: true,
                validValues: ['1', '2', 'M', 'F', 'Male', 'Female', '男', '女'],
                weight: 7
            },
            passportExpiry: {
                type: 'date',
                required: true,
                patterns: [/^\d{2}\/\d{2}\/\d{4}$/],
                dateRange: { min: new Date().toLocaleDateString('en-GB'), max: '31/12/2040' },
                weight: 9
            },
            
            // 联系信息字段
            email: {
                type: 'email',
                required: true,
                patterns: [/^[^\s@]+@[^\s@]+\.[^\s@]+$/],
                weight: 9
            },
            confirmEmail: {
                type: 'email',
                required: true,
                patterns: [/^[^\s@]+@[^\s@]+\.[^\s@]+$/],
                weight: 8
            },
            countryCode: {
                type: 'select',
                required: true,
                validValues: ['+86', '+60', '+65', '+1', '+66', '+62', '+81', '+82', '+91', '+44'],
                weight: 6
            },
            mobileNo: {
                type: 'tel',
                required: true,
                patterns: [/^\d{8,15}$/],
                minLength: 8,
                maxLength: 15,
                weight: 8
            },
            
            // 旅行信息字段
            arrivalDate: {
                type: 'date',
                required: true,
                patterns: [/^\d{2}\/\d{2}\/\d{4}$/],
                dateRange: { min: new Date().toLocaleDateString('en-GB'), max: '31/12/2025' },
                weight: 10
            },
            departureDate: {
                type: 'date',
                required: true,
                patterns: [/^\d{2}\/\d{2}\/\d{4}$/],
                dateRange: { min: new Date().toLocaleDateString('en-GB'), max: '31/12/2025' },
                weight: 10
            },
            flightNo: {
                type: 'text',
                required: true,
                patterns: [/^[A-Z]{2,3}\d{1,4}[A-Z]?$/],
                minLength: 3,
                maxLength: 8,
                weight: 7
            },
            modeOfTravel: {
                type: 'select',
                required: true,
                validValues: ['AIR', 'SEA', 'LAND', '航空', '海运', '陆路'],
                weight: 6
            },
            lastPort: {
                type: 'select',
                required: true,
                validValues: ['SGP', 'BKK', 'CGK', 'HKG', 'TPE', 'ICN', 'NRT'],
                weight: 7
            },
            
            // 住宿信息字段
            accommodation: {
                type: 'select',
                required: true,
                validValues: ['01', '02', '03', '04', 'Hotel', 'Homestay', 'Friend', 'Other'],
                weight: 6
            },
            address: {
                type: 'text',
                required: true,
                patterns: [/^.{5,100}$/],
                minLength: 5,
                maxLength: 100,
                weight: 8
            },
            address2: {
                type: 'text',
                required: false,
                patterns: [/^.{0,50}$/],
                maxLength: 50,
                weight: 3
            },
            state: {
                type: 'select',
                required: true,
                validValues: ['14', '10', '07', '01', '02', '03', '04', '05', '06', '08', '09', '11', '12', '13', '15', '16'],
                weight: 7
            },
            postcode: {
                type: 'text',
                required: true,
                patterns: [/^\d{5}$/],
                minLength: 5,
                maxLength: 5,
                weight: 6
            },
            city: {
                type: 'select',
                required: true,
                validValues: ['1400', '1000', '0700', '0100', '0118', '0124', '0127', '0136', '0142', '0144', '0196', '0200'],
                weight: 6
            }
        };
        
        // 置信度阈值
        this.confidenceThresholds = {
            high: 85,      // 高置信度
            medium: 70,    // 中等置信度
            low: 50,       // 低置信度
            critical: 30   // 极低置信度
        };
        
        // 评估权重
        this.evaluationWeights = {
            formatMatch: 0.3,      // 格式匹配权重
            lengthCheck: 0.2,      // 长度检查权重
            patternMatch: 0.25,    // 模式匹配权重
            valueValidation: 0.15, // 值验证权重
            contextRelevance: 0.1  // 上下文相关性权重
        };
        
        // 评估历史
        this.evaluationHistory = [];
        this.maxHistorySize = 100;
        
        // 统计信息
        this.stats = {
            totalEvaluations: 0,
            highConfidenceCount: 0,
            mediumConfidenceCount: 0,
            lowConfidenceCount: 0,
            criticalConfidenceCount: 0,
            averageConfidence: 0,
            lastUpdated: Date.now()
        };
    }
    
    /**
     * 评估解析结果的置信度
     * @param {Object} parsedData 解析的数据
     * @param {Object} context 上下文信息
     * @returns {Object} 置信度评估结果
     */
    evaluateConfidence(parsedData, context = {}) {
        console.log('🔍 开始置信度评估:', parsedData);
        
        const evaluationResult = {
            overall: {
                confidence: 0,
                level: 'unknown',
                issues: [],
                recommendations: []
            },
            fields: {},
            summary: {
                totalFields: 0,
                highConfidence: 0,
                mediumConfidence: 0,
                lowConfidence: 0,
                criticalConfidence: 0
            },
            timestamp: Date.now(),
            evaluationId: this.generateEvaluationId()
        };
        
        let totalWeightedConfidence = 0;
        let totalWeight = 0;
        
        // 评估每个字段
        for (const [fieldKey, value] of Object.entries(parsedData)) {
            const fieldConfig = this.fieldTypes[fieldKey];
            if (!fieldConfig) {
                console.warn(`未知字段类型: ${fieldKey}`);
                continue;
            }
            
            const fieldEvaluation = this.evaluateField(fieldKey, value, fieldConfig, context);
            evaluationResult.fields[fieldKey] = fieldEvaluation;
            evaluationResult.summary.totalFields++;
            
            // 累计加权置信度
            totalWeightedConfidence += fieldEvaluation.confidence * fieldConfig.weight;
            totalWeight += fieldConfig.weight;
            
            // 统计置信度分布
            this.categorizeConfidence(fieldEvaluation.confidence, evaluationResult.summary);
            
            // 收集问题和建议
            if (fieldEvaluation.issues.length > 0) {
                evaluationResult.overall.issues.push(...fieldEvaluation.issues);
            }
            if (fieldEvaluation.recommendations.length > 0) {
                evaluationResult.overall.recommendations.push(...fieldEvaluation.recommendations);
            }
        }
        
        // 计算整体置信度
        evaluationResult.overall.confidence = totalWeight > 0 
            ? Math.round(totalWeightedConfidence / totalWeight)
            : 0;
        
        evaluationResult.overall.level = this.getConfidenceLevel(evaluationResult.overall.confidence);
        
        // 生成整体建议
        this.generateOverallRecommendations(evaluationResult);
        
        // 记录评估历史
        this.recordEvaluation(evaluationResult);
        
        // 更新统计
        this.updateStats(evaluationResult);
        
        console.log('📊 置信度评估完成:', evaluationResult);
        return evaluationResult;
    }
    
    /**
     * 评估单个字段
     * @param {string} fieldKey 字段键
     * @param {any} value 字段值
     * @param {Object} config 字段配置
     * @param {Object} context 上下文
     * @returns {Object} 字段评估结果
     */
    evaluateField(fieldKey, value, config, context) {
        const evaluation = {
            fieldKey,
            value,
            confidence: 0,
            level: 'unknown',
            issues: [],
            recommendations: [],
            details: {
                formatScore: 0,
                lengthScore: 0,
                patternScore: 0,
                valueScore: 0,
                contextScore: 0
            }
        };
        
        // 检查必填字段
        if (config.required && (!value || value.toString().trim() === '')) {
            evaluation.issues.push('必填字段为空');
            evaluation.recommendations.push('请提供此字段的值');
            evaluation.confidence = 0;
            evaluation.level = 'critical';
            return evaluation;
        }
        
        // 如果字段为空且非必填，给予中等置信度
        if (!value || value.toString().trim() === '') {
            evaluation.confidence = 70;
            evaluation.level = 'medium';
            return evaluation;
        }
        
        const stringValue = value.toString().trim();
        
        // 1. 格式检查
        evaluation.details.formatScore = this.checkFormat(stringValue, config);
        
        // 2. 长度检查
        evaluation.details.lengthScore = this.checkLength(stringValue, config);
        
        // 3. 模式匹配
        evaluation.details.patternScore = this.checkPattern(stringValue, config);
        
        // 4. 值验证
        evaluation.details.valueScore = this.checkValue(stringValue, config);
        
        // 5. 上下文相关性
        evaluation.details.contextScore = this.checkContext(fieldKey, stringValue, context);
        
        // 计算综合置信度
        evaluation.confidence = Math.round(
            evaluation.details.formatScore * this.evaluationWeights.formatMatch +
            evaluation.details.lengthScore * this.evaluationWeights.lengthCheck +
            evaluation.details.patternScore * this.evaluationWeights.patternMatch +
            evaluation.details.valueScore * this.evaluationWeights.valueValidation +
            evaluation.details.contextScore * this.evaluationWeights.contextRelevance
        );
        
        evaluation.level = this.getConfidenceLevel(evaluation.confidence);
        
        // 生成问题和建议
        this.generateFieldIssuesAndRecommendations(evaluation, config);
        
        return evaluation;
    }
    
    /**
     * 检查格式
     */
    checkFormat(value, config) {
        switch (config.type) {
            case 'email':
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) ? 100 : 0;
            case 'date':
                return /^\d{2}\/\d{2}\/\d{4}$/.test(value) ? 100 : 0;
            case 'tel':
                return /^\d+$/.test(value) ? 100 : 0;
            case 'text':
                return value.length > 0 ? 100 : 0;
            case 'select':
                return value.length > 0 ? 100 : 0;
            default:
                return 80;
        }
    }
    
    /**
     * 检查长度
     */
    checkLength(value, config) {
        let score = 100;
        
        if (config.minLength && value.length < config.minLength) {
            score = Math.max(0, score - (config.minLength - value.length) * 10);
        }
        
        if (config.maxLength && value.length > config.maxLength) {
            score = Math.max(0, score - (value.length - config.maxLength) * 10);
        }
        
        return Math.min(100, score);
    }
    
    /**
     * 检查模式匹配
     */
    checkPattern(value, config) {
        if (!config.patterns || config.patterns.length === 0) {
            return 80; // 没有模式要求，给予默认分数
        }
        
        for (const pattern of config.patterns) {
            if (pattern.test(value)) {
                return 100;
            }
        }
        
        return 20; // 不匹配任何模式
    }
    
    /**
     * 检查值有效性
     */
    checkValue(value, config) {
        // 检查有效值列表
        if (config.validValues && config.validValues.length > 0) {
            const isValid = config.validValues.some(validValue => 
                value.toLowerCase() === validValue.toLowerCase()
            );
            return isValid ? 100 : 30;
        }
        
        // 检查日期范围
        if (config.dateRange && config.type === 'date') {
            return this.checkDateRange(value, config.dateRange);
        }
        
        return 80; // 没有特殊验证要求
    }
    
    /**
     * 检查日期范围
     */
    checkDateRange(dateString, range) {
        try {
            const [day, month, year] = dateString.split('/').map(Number);
            const date = new Date(year, month - 1, day);
            
            const minDate = new Date(range.min.split('/').reverse().join('-'));
            const maxDate = new Date(range.max.split('/').reverse().join('-'));
            
            if (date >= minDate && date <= maxDate) {
                return 100;
            } else {
                return 20;
            }
        } catch (error) {
            return 0;
        }
    }
    
    /**
     * 检查上下文相关性
     */
    checkContext(fieldKey, value, context) {
        // 检查邮箱一致性
        if (fieldKey === 'confirmEmail' && context.email) {
            return value === context.email ? 100 : 0;
        }
        
        // 检查日期逻辑性
        if (fieldKey === 'departureDate' && context.arrivalDate) {
            try {
                const arrivalDate = new Date(context.arrivalDate.split('/').reverse().join('-'));
                const departureDate = new Date(value.split('/').reverse().join('-'));
                return departureDate > arrivalDate ? 100 : 20;
            } catch (error) {
                return 50;
            }
        }
        
        return 80; // 默认上下文分数
    }
    
    /**
     * 获取置信度级别
     */
    getConfidenceLevel(confidence) {
        if (confidence >= this.confidenceThresholds.high) return 'high';
        if (confidence >= this.confidenceThresholds.medium) return 'medium';
        if (confidence >= this.confidenceThresholds.low) return 'low';
        return 'critical';
    }
    
    /**
     * 分类置信度
     */
    categorizeConfidence(confidence, summary) {
        const level = this.getConfidenceLevel(confidence);
        switch (level) {
            case 'high':
                summary.highConfidence++;
                break;
            case 'medium':
                summary.mediumConfidence++;
                break;
            case 'low':
                summary.lowConfidence++;
                break;
            case 'critical':
                summary.criticalConfidence++;
                break;
        }
    }
    
    /**
     * 生成字段问题和建议
     */
    generateFieldIssuesAndRecommendations(evaluation, config) {
        const { confidence, details, value } = evaluation;
        
        if (confidence < this.confidenceThresholds.critical) {
            evaluation.issues.push('字段值可能存在严重问题');
            evaluation.recommendations.push('建议重新检查并修正此字段');
        } else if (confidence < this.confidenceThresholds.low) {
            evaluation.issues.push('字段值可能不准确');
            evaluation.recommendations.push('建议验证此字段的准确性');
        } else if (confidence < this.confidenceThresholds.medium) {
            evaluation.issues.push('字段值需要确认');
            evaluation.recommendations.push('建议在填充前确认此字段');
        }
        
        // 具体问题检查
        if (details.formatScore < 50) {
            evaluation.issues.push('格式不符合要求');
            evaluation.recommendations.push(`请确保${config.type}格式正确`);
        }
        
        if (details.lengthScore < 50) {
            evaluation.issues.push('长度不符合要求');
            if (config.minLength) {
                evaluation.recommendations.push(`最少需要${config.minLength}个字符`);
            }
            if (config.maxLength) {
                evaluation.recommendations.push(`最多允许${config.maxLength}个字符`);
            }
        }
        
        if (details.patternScore < 50) {
            evaluation.issues.push('格式模式不匹配');
            evaluation.recommendations.push('请检查字段格式是否正确');
        }
        
        if (details.valueScore < 50) {
            evaluation.issues.push('值不在有效范围内');
            if (config.validValues) {
                evaluation.recommendations.push(`有效值包括: ${config.validValues.join(', ')}`);
            }
        }
    }

    /**
     * 生成整体建议
     */
    generateOverallRecommendations(evaluationResult) {
        const { overall, summary } = evaluationResult;

        if (overall.confidence >= this.confidenceThresholds.high) {
            overall.recommendations.push('解析结果质量很高，可以直接使用');
        } else if (overall.confidence >= this.confidenceThresholds.medium) {
            overall.recommendations.push('解析结果基本可用，建议检查标记的字段');
        } else if (overall.confidence >= this.confidenceThresholds.low) {
            overall.recommendations.push('解析结果需要仔细检查，建议手动验证多个字段');
        } else {
            overall.recommendations.push('解析结果质量较低，建议重新解析或手动输入');
        }

        if (summary.criticalConfidence > 0) {
            overall.recommendations.push(`有${summary.criticalConfidence}个字段存在严重问题，必须修正`);
        }

        if (summary.lowConfidence > 0) {
            overall.recommendations.push(`有${summary.lowConfidence}个字段置信度较低，建议检查`);
        }

        // 特殊建议
        if (summary.totalFields < 10) {
            overall.recommendations.push('解析字段数量较少，可能需要补充更多信息');
        }

        if (overall.issues.length > summary.totalFields * 0.5) {
            overall.recommendations.push('问题字段比例较高，建议重新解析原始数据');
        }
    }

    /**
     * 记录评估历史
     */
    recordEvaluation(evaluationResult) {
        const historyEntry = {
            id: evaluationResult.evaluationId,
            timestamp: evaluationResult.timestamp,
            overallConfidence: evaluationResult.overall.confidence,
            totalFields: evaluationResult.summary.totalFields,
            highConfidence: evaluationResult.summary.highConfidence,
            mediumConfidence: evaluationResult.summary.mediumConfidence,
            lowConfidence: evaluationResult.summary.lowConfidence,
            criticalConfidence: evaluationResult.summary.criticalConfidence,
            issuesCount: evaluationResult.overall.issues.length
        };

        this.evaluationHistory.unshift(historyEntry);

        // 限制历史记录大小
        if (this.evaluationHistory.length > this.maxHistorySize) {
            this.evaluationHistory = this.evaluationHistory.slice(0, this.maxHistorySize);
        }
    }

    /**
     * 更新统计信息
     */
    updateStats(evaluationResult) {
        this.stats.totalEvaluations++;
        this.stats.highConfidenceCount += evaluationResult.summary.highConfidence;
        this.stats.mediumConfidenceCount += evaluationResult.summary.mediumConfidence;
        this.stats.lowConfidenceCount += evaluationResult.summary.lowConfidence;
        this.stats.criticalConfidenceCount += evaluationResult.summary.criticalConfidence;

        // 更新平均置信度
        const totalEvaluations = this.stats.totalEvaluations;
        if (totalEvaluations === 1) {
            this.stats.averageConfidence = evaluationResult.overall.confidence;
        } else {
            this.stats.averageConfidence =
                (this.stats.averageConfidence * (totalEvaluations - 1) + evaluationResult.overall.confidence) / totalEvaluations;
        }

        this.stats.lastUpdated = Date.now();
    }

    /**
     * 生成评估ID
     */
    generateEvaluationId() {
        return 'eval_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 获取置信度颜色
     */
    getConfidenceColor(confidence) {
        if (confidence >= this.confidenceThresholds.high) return '#28a745';
        if (confidence >= this.confidenceThresholds.medium) return '#ffc107';
        if (confidence >= this.confidenceThresholds.low) return '#fd7e14';
        return '#dc3545';
    }

    /**
     * 获取置信度图标
     */
    getConfidenceIcon(confidence) {
        if (confidence >= this.confidenceThresholds.high) return '✅';
        if (confidence >= this.confidenceThresholds.medium) return '⚠️';
        if (confidence >= this.confidenceThresholds.low) return '🔶';
        return '❌';
    }

    /**
     * 获取置信度描述
     */
    getConfidenceDescription(confidence) {
        if (confidence >= this.confidenceThresholds.high) return '高置信度 - 数据可靠';
        if (confidence >= this.confidenceThresholds.medium) return '中等置信度 - 建议检查';
        if (confidence >= this.confidenceThresholds.low) return '低置信度 - 需要验证';
        return '极低置信度 - 必须修正';
    }

    /**
     * 创建置信度可视化
     */
    createConfidenceVisualization(evaluationResult) {
        const visualization = {
            overall: this.createOverallVisualization(evaluationResult.overall),
            fields: {},
            summary: this.createSummaryVisualization(evaluationResult.summary)
        };

        // 为每个字段创建可视化
        for (const [fieldKey, fieldEval] of Object.entries(evaluationResult.fields)) {
            visualization.fields[fieldKey] = this.createFieldVisualization(fieldEval);
        }

        return visualization;
    }

    /**
     * 创建整体可视化
     */
    createOverallVisualization(overall) {
        return {
            confidence: overall.confidence,
            level: overall.level,
            color: this.getConfidenceColor(overall.confidence),
            icon: this.getConfidenceIcon(overall.confidence),
            description: this.getConfidenceDescription(overall.confidence),
            progressBar: {
                width: overall.confidence,
                color: this.getConfidenceColor(overall.confidence)
            }
        };
    }

    /**
     * 创建字段可视化
     */
    createFieldVisualization(fieldEval) {
        return {
            confidence: fieldEval.confidence,
            level: fieldEval.level,
            color: this.getConfidenceColor(fieldEval.confidence),
            icon: this.getConfidenceIcon(fieldEval.confidence),
            description: this.getConfidenceDescription(fieldEval.confidence),
            hasIssues: fieldEval.issues.length > 0,
            issuesCount: fieldEval.issues.length,
            recommendationsCount: fieldEval.recommendations.length,
            badge: {
                text: `${fieldEval.confidence}%`,
                color: this.getConfidenceColor(fieldEval.confidence),
                class: `confidence-${fieldEval.level}`
            }
        };
    }

    /**
     * 创建摘要可视化
     */
    createSummaryVisualization(summary) {
        const total = summary.totalFields;

        return {
            distribution: {
                high: { count: summary.highConfidence, percentage: Math.round((summary.highConfidence / total) * 100) },
                medium: { count: summary.mediumConfidence, percentage: Math.round((summary.mediumConfidence / total) * 100) },
                low: { count: summary.lowConfidence, percentage: Math.round((summary.lowConfidence / total) * 100) },
                critical: { count: summary.criticalConfidence, percentage: Math.round((summary.criticalConfidence / total) * 100) }
            },
            chart: {
                data: [
                    { label: '高置信度', value: summary.highConfidence, color: '#28a745' },
                    { label: '中等置信度', value: summary.mediumConfidence, color: '#ffc107' },
                    { label: '低置信度', value: summary.lowConfidence, color: '#fd7e14' },
                    { label: '极低置信度', value: summary.criticalConfidence, color: '#dc3545' }
                ]
            }
        };
    }

    /**
     * 获取统计摘要
     */
    getStatsSummary() {
        return {
            ...this.stats,
            evaluationHistory: this.evaluationHistory.slice(0, 10), // 最近10次评估
            confidenceDistribution: {
                high: this.stats.highConfidenceCount,
                medium: this.stats.mediumConfidenceCount,
                low: this.stats.lowConfidenceCount,
                critical: this.stats.criticalConfidenceCount
            }
        };
    }

    /**
     * 清除历史数据
     */
    clearHistory() {
        this.evaluationHistory = [];
        this.stats = {
            totalEvaluations: 0,
            highConfidenceCount: 0,
            mediumConfidenceCount: 0,
            lowConfidenceCount: 0,
            criticalConfidenceCount: 0,
            averageConfidence: 0,
            lastUpdated: Date.now()
        };

        console.log('🗑️ 置信度评估历史已清除');
    }
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ConfidenceEvaluator;
} else {
    window.ConfidenceEvaluator = ConfidenceEvaluator;
}
