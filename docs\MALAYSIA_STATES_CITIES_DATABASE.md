# 🇲🇾 马来西亚完整州属城市数据库

## 概述

这是一个包含马来西亚16个州属及其主要城市的完整数据库，专为MDAC（马来西亚数字入境卡）Chrome扩展而设计。数据库包含详细的城市代码、中英文名称对照、邮政编码范围等信息。

## 📊 数据统计

- **16个州属** - 包括13个州、2个联邦直辖区、1个联邦领土
- **200+个城市** - 覆盖主要城市和旅游目的地
- **双语支持** - 完整的中英文对照
- **邮编映射** - 准确的邮政编码范围

## 🗂️ 文件结构

```
config/
└── malaysia-states-cities.json    # 完整的州属城市数据
utils/
├── mdac-validator.js              # 智能验证和匹配工具
└── enhanced-form-filler.js        # 增强表单填充器
test/
└── malaysia-data-test.html        # 数据测试页面
```

## 📋 州属列表

| 代码 | 英文名称 | 中文名称 | 主要城市数量 |
|------|---------|---------|------------|
| 01 | Johor | 柔佛 | 19 |
| 02 | Kedah | 吉打 | 13 |
| 03 | Kelantan | 吉兰丹 | 11 |
| 04 | Melaka | 马六甲 | 11 |
| 05 | Negeri Sembilan | 森美兰 | 11 |
| 06 | Pahang | 彭亨 | 15 |
| 07 | Pulau Pinang | 槟城 | 19 |
| 08 | Perak | 霹雳 | 20 |
| 09 | Perlis | 玻璃市 | 6 |
| 10 | Selangor | 雪兰莪 | 22 |
| 11 | Terengganu | 登嘉楼 | 9 |
| 12 | Sabah | 沙巴 | 19 |
| 13 | Sarawak | 砂拉越 | 20 |
| 14 | Kuala Lumpur | 吉隆坡 | 18 |
| 15 | Labuan | 纳闽 | 2 |
| 16 | Putrajaya | 布城 | 21 |

## 🔧 使用方法

### 1. 基础用法

```javascript
// 加载数据
const response = await fetch('config/malaysia-states-cities.json');
const data = await response.json();

// 获取所有州属
console.log(data.states);

// 获取特定州属的城市
const johorCities = data.cities['01'].cities;
```

### 2. 智能匹配

```javascript
// 使用验证器进行智能匹配
const validator = new MDACValidator();

// 州属匹配
const stateCode = validator.smartStateMapping('柔佛'); // 返回 '01'
const stateCode2 = validator.smartStateMapping('Johor'); // 返回 '01'

// 城市匹配
const cityCode = validator.smartCityMapping('新山'); // 返回 '0100'
const cityCode2 = validator.smartCityMapping('Johor Bahru'); // 返回 '0100'

// 邮编匹配
const cityFromPostcode = validator.getCityByPostcode('81300'); // 返回对应城市代码
```

### 3. 高级功能

```javascript
// 获取州属下的所有城市
const cities = validator.getCitiesByState('01');

// 验证城市州属匹配
const isValid = validator.validateCityStateMatch('0100', '01');

// 获取热门旅游目的地
const popular = validator.getPopularDestinations();
```

## 🎯 热门旅游目的地

数据库特别标记了10个热门旅游目的地：

1. **新山 (Johor Bahru)** - 购物、乐高乐园
2. **乔治市 (George Town)** - 联合国世界遗产
3. **吉隆坡 (Kuala Lumpur)** - 首都、双峰塔
4. **马六甲 (Melaka)** - 历史古城
5. **兰卡威 (Langkawi)** - 海岛度假村
6. **金马伦高原 (Cameron Highlands)** - 山区度假村
7. **亚庇 (Kota Kinabalu)** - 神山、沙巴
8. **古晋 (Kuching)** - 砂拉越文化
9. **波德申 (Port Dickson)** - 海滩度假村
10. **峇六拜 (Bayan Lepas)** - 槟城机场区域

## 🧪 测试

打开 `test/malaysia-data-test.html` 进行交互式测试：

1. **州属匹配测试** - 测试中英文州属名称匹配
2. **城市匹配测试** - 测试中英文城市名称匹配
3. **邮编匹配测试** - 测试邮政编码到城市的匹配
4. **州属城市列表** - 查看每个州属下的所有城市
5. **热门目的地** - 查看标记的热门旅游城市
6. **数据完整性验证** - 验证数据的完整性和一致性

## 📝 数据格式

### 州属数据格式
```json
{
  "states": {
    "01": "Johor",
    "02": "Kedah"
  }
}
```

### 城市数据格式
```json
{
  "cities": {
    "01": {
      "stateName": "Johor",
      "cities": {
        "0100": {
          "name": "Johor Bahru",
          "postcode": "79000-82999",
          "chinese": "新山"
        }
      }
    }
  }
}
```

### 映射数据格式
```json
{
  "cityMapping": {
    "english_to_chinese": {
      "Johor Bahru": "新山"
    },
    "chinese_to_english": {
      "新山": "Johor Bahru"
    }
  }
}
```

## 🔄 更新日志

### v2.0.0 (2025-01-10)
- ✅ 完整的16个州属数据
- ✅ 200+个城市详细信息
- ✅ 中英文双语支持
- ✅ 邮政编码范围映射
- ✅ 智能匹配算法
- ✅ 热门目的地标记
- ✅ 数据验证功能
- ✅ 交互式测试页面

### v1.0.0
- 基础州属城市数据

## 🤝 贡献

如果发现数据错误或需要添加新的城市，请提出Issue或Pull Request。

## 📄 许可证

MIT License - 可自由使用于个人和商业项目。

---

**注意**: 此数据库专为MDAC表单填充而设计，邮政编码范围可能不完全精确，仅作为匹配参考使用。
