# MDAC AI Chrome扩展 - 自动解析功能使用指南

## 功能概述

自动解析功能为MDAC AI Chrome扩展的侧边栏界面添加了智能化的用户体验，当用户在文本输入框或图片上传区域输入/上传内容后，系统会自动触发AI解析，无需手动点击解析按钮。

## 主要特性

### 1. 自动触发机制
- **防抖延迟**: 用户停止输入500毫秒后自动触发解析
- **智能检测**: 自动检测输入内容变化
- **双输入源支持**: 同时支持文本输入和图片上传的自动解析

### 2. 用户控制选项
- **开关控制**: 每个解析区域都有独立的自动解析开关
- **手动备选**: 保留手动解析按钮作为备选方案
- **取消功能**: 在倒计时期间可以取消自动解析

### 3. 视觉反馈
- **倒计时提示**: 显示"3秒后自动解析..."的倒计时
- **状态指示器**: 清晰的加载状态和进度指示
- **动画效果**: 平滑的淡入淡出动画

## 使用方法

### 个人信息自动解析
1. 在"AI解析个人信息"区域的文本框中输入或粘贴内容
2. 确保右上角的"自动解析"开关处于开启状态
3. 停止输入后，系统会显示3秒倒计时
4. 倒计时结束后自动开始AI解析
5. 解析完成后自动显示结果和数据预览

### 旅行信息自动解析
1. 在"AI解析旅行信息"区域的文本框中输入或粘贴内容
2. 确保右上角的"自动解析"开关处于开启状态
3. 系统会自动处理机票信息、酒店预订等内容
4. 解析结果会自动填充到相应的表单字段

### 取消自动解析
- 在倒计时期间点击"取消"按钮
- 或者继续编辑输入内容（会重置倒计时）
- 或者关闭自动解析开关

## 设置选项

### 自动解析开关
- **位置**: 每个解析区域的右上角
- **状态**: 默认开启
- **保存**: 设置会自动保存到Chrome存储

### 延迟时间
- **默认**: 3秒防抖延迟
- **目的**: 避免在用户输入过程中频繁触发解析
- **可调整**: 可在代码中修改 `autoParseSettings.delay` 值

## 技术实现

### 防抖机制
```javascript
// 使用setTimeout和clearTimeout实现防抖
handleAutoParseInput(type, content) {
    if (this.autoParseTimeouts[type]) {
        clearTimeout(this.autoParseTimeouts[type]);
    }
    
    this.autoParseTimeouts[type] = setTimeout(() => {
        this.triggerAutoParse(type);
    }, this.autoParseSettings.delay);
}
```

### 状态管理
- **倒计时显示**: 实时更新倒计时文本
- **状态切换**: 自动在等待、解析中、完成状态间切换
- **错误处理**: 解析失败时显示错误信息

### 数据持久化
- **设置保存**: 自动解析开关状态保存到Chrome存储
- **设置恢复**: 扩展启动时自动恢复用户设置

## 兼容性

### 现有功能
- **完全兼容**: 不影响现有的手动解析功能
- **数据共享**: 自动解析和手动解析使用相同的AI引擎
- **结果一致**: 解析结果格式和质量保持一致

### 表单填充
- **无缝集成**: 自动解析完成后可直接进行表单填充
- **数据验证**: 解析结果经过相同的验证流程
- **预览功能**: 自动显示数据预览供用户确认

## 故障排除

### 常见问题
1. **自动解析不触发**
   - 检查自动解析开关是否开启
   - 确认输入内容不为空
   - 检查是否在倒计时期间继续输入

2. **倒计时显示异常**
   - 刷新侧边栏页面
   - 检查浏览器控制台是否有错误信息

3. **解析结果不准确**
   - 尝试使用手动解析进行对比
   - 检查输入内容的格式和完整性

### 调试信息
- 打开浏览器开发者工具
- 查看控制台输出的调试信息
- 检查Chrome扩展的错误日志

## 更新日志

### v2.0.0
- 新增自动解析功能
- 添加防抖机制和倒计时显示
- 实现用户设置的持久化保存
- 优化用户界面和交互体验
