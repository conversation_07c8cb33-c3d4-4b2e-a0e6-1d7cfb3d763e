# 城市查看器功能使用指南

## 功能概述

城市查看器是MDAC AI智能助手的一个重要功能，提供了完整的马来西亚州属和城市数据库，包含：

- **16个州属** 的完整信息
- **237个城市** 的详细资料
- **中英文对照** 的城市和州属名称
- **邮编信息** 和地理范围
- **热门目的地** 推荐

## 主要功能

### 1. 城市浏览
- 📋 **列表视图**：紧凑的列表形式显示城市信息
- 📱 **网格视图**：卡片式布局，更直观的展示

### 2. 搜索功能
- 🔍 **智能搜索**：支持中文、英文、州属名称的模糊搜索
- 实时搜索结果，无需点击搜索按钮
- 支持部分匹配和拼音搜索

### 3. 过滤功能
- 🏛️ **州属过滤**：按州属筛选城市
- 🌟 **热门目的地**：快速查看旅游热点城市
- 自动统计显示的城市数量

### 4. 城市信息
每个城市显示：
- 英文名称和中文名称
- 所属州属
- 邮编范围
- 快速使用按钮

## 使用方法

### 在侧边栏中使用

1. **打开城市查看器**
   - 点击工具栏中的 🏙️ "城市查看器" 按钮
   - 系统会自动加载完整的城市数据库

2. **搜索城市**
   - 在搜索框中输入城市名称（中文或英文）
   - 支持模糊搜索，如输入"吉隆坡"、"KL"、"Kuala"等
   - 搜索结果会实时更新

3. **按州属过滤**
   - 使用州属下拉列表选择特定州属
   - 只显示该州属下的城市

4. **查看热门目的地**
   - 点击"热门目的地"按钮
   - 显示马来西亚的主要旅游城市

5. **切换视图模式**
   - 📋 列表视图：紧凑显示，适合快速浏览
   - 📱 网格视图：卡片式显示，信息更详细

6. **使用城市数据**
   - 点击城市项的"使用"按钮
   - 城市信息会自动填充到当前表单

### 支持的搜索示例

| 搜索词 | 匹配结果 |
|--------|----------|
| 吉隆坡 | Kuala Lumpur |
| 新山 | Johor Bahru |
| George | George Town (槟城) |
| Ipoh | 怡保 |
| 柔佛 | 柔佛州所有城市 |
| 50000 | 可能匹配邮编范围内的城市 |

## 数据结构

### 城市数据格式
```json
{
  "code": "0100",
  "name": "Johor Bahru",
  "chinese": "新山",
  "postcode": "79000-82999",
  "stateCode": "01",
  "stateName": "Johor",
  "displayName": "Johor Bahru (新山)",
  "fullDisplay": "Johor Bahru (新山) - Johor"
}
```

### 热门目的地
包含以下知名城市：
- 新山 (Johor Bahru) - 购物、乐高乐园
- 乔治市 (George Town) - 世界文化遗产
- 吉隆坡 (Kuala Lumpur) - 首都、双峰塔
- 马六甲 (Melaka) - 历史古城
- 兰卡威 (Langkawi) - 度假岛屿
- 金马伦高原 (Cameron Highlands) - 山地度假村
- 亚庇 (Kota Kinabalu) - 沙巴、神山
- 古晋 (Kuching) - 砂拉越文化
- 波德申 (Port Dickson) - 海滩度假村
- 峇六拜 (Bayan Lepas) - 槟城机场区

## 技术特性

### 智能匹配
- 支持中英文混合搜索
- 忽略大小写
- 部分匹配算法
- 拼音和简写支持

### 性能优化
- 数据预加载和缓存
- 实时搜索防抖处理
- 虚拟滚动支持（大量数据时）
- 响应式布局适配

### 数据完整性
- 所有16个州属覆盖
- 237个城市的完整信息
- 准确的邮编范围
- 定期更新维护

## 常见问题

### Q: 搜索不到某个城市？
A: 请尝试：
1. 使用城市的英文名称
2. 使用州属名称查找
3. 检查拼写是否正确
4. 尝试使用城市的中文名称

### Q: 城市信息不准确？
A: 城市数据基于官方资料整理，如发现错误请：
1. 检查是否为最新版本
2. 确认城市是否存在名称变更
3. 核对邮编范围是否正确

### Q: 如何添加新城市？
A: 城市数据存储在 `config/malaysia-states-cities.json` 文件中，按照现有格式添加新城市信息。

## 更新日志

### v1.0.0 (2024-01-10)
- ✅ 初始版本发布
- ✅ 完整的马来西亚城市数据库
- ✅ 智能搜索和过滤功能
- ✅ 双视图模式支持
- ✅ 热门目的地推荐
- ✅ 表单自动填充集成

---

**注意**：城市查看器功能需要Chrome扩展环境才能完整运行，包括数据加载和表单填充功能。在测试页面中，表单填充功能会以提示框的形式模拟显示。
