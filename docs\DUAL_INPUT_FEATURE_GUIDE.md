# Chrome扩展双输入源功能使用指南

## 🎯 功能概述

Chrome扩展现已支持双输入源功能，完美结合AI智能解析和用户手动补充信息，为MDAC表单填充提供更完整、更灵活的数据来源。

## 📋 功能特性

### 1. 智能内容解析（第一输入源）
- **动态解析**：每次输入内容都会重新解析
- **AI驱动**：使用Gemini AI提取个人基础信息
- **支持格式**：文本内容、图片文字提取
- **解析字段**：姓名、护照号、出生日期、性别、国籍、护照到期时间等

### 2. 补充信息输入（第二输入源）
- **持久化存储**：内容自动保存到本地，浏览器重启后保留
- **实时保存**：输入时自动保存，无需手动操作
- **智能解析**：自动识别常见字段格式
- **状态显示**：实时显示保存的字段数量和字符数

### 3. 数据合并与预览
- **智能合并**：AI解析数据优先级更高，补充信息作为补充
- **预览功能**：填充前显示完整的合并数据预览
- **冲突处理**：清晰标识被覆盖的字段
- **一键填充**：确认后统一插入MDAC表单

## 🚀 使用流程

### 步骤1：AI智能解析
1. 在第一个输入框中粘贴或输入内容
2. 支持的内容类型：
   - 护照信息文本
   - 个人资料文档
   - 邮件内容
   - 图片文字（点击"上传图片"）
3. 点击"开始AI解析"
4. 查看解析结果和完整度指示器

### 步骤2：补充信息输入
1. 在第二个输入框中输入补充信息
2. 支持的字段格式：
   ```
   邮箱：<EMAIL>
   电话：+60*********
   航班号：MH123
   住宿地址：123 Main Street, Kuala Lumpur
   到达日期：2024-01-15
   离开日期：2024-01-20
   ```
3. 内容自动保存，状态栏显示保存情况

### 步骤3：数据合并与填充
1. 点击"插入表单"按钮
2. 如果有多个数据源，自动显示合并预览
3. 查看数据合并结果：
   - 🧠 AI解析数据（优先级高）
   - 📝 补充信息数据
   - 🎯 最终合并结果
4. 点击"确认填充表单"完成填充

## 📊 支持的字段类型

### AI解析字段（个人基础信息）
| 字段 | 说明 | 示例 |
|------|------|------|
| name | 英文姓名 | John Smith |
| passportNo | 护照号码 | A12345678 |
| dateOfBirth | 出生日期 | 1990-01-15 |
| nationality | 国籍 | Malaysian |
| gender | 性别 | Male/Female |
| passportExpiry | 护照到期日期 | 2030-01-15 |

### 补充信息字段
| 字段 | 格式示例 | 说明 |
|------|----------|------|
| email | 邮箱：<EMAIL> | 邮箱地址 |
| countryCode | 国家代码：+60 | 电话国家代码 |
| mobileNo | 电话：********* | 手机号码 |
| flightNo | 航班号：MH123 | 航班信息 |
| arrivalDate | 到达日期：2024-01-15 | 到达时间 |
| departureDate | 离开日期：2024-01-20 | 离开时间 |
| accommodation | 住宿类型：Hotel | 住宿类型 |
| address | 地址：123 Main Street | 住宿地址 |
| state | 州：Kuala Lumpur | 州/省份 |
| postcode | 邮编：50000 | 邮政编码 |
| city | 城市：Kuala Lumpur | 城市名称 |
| modeOfTravel | 旅行方式：Air | 交通方式 |
| lastPort | 最后港口：Singapore | 上一个港口 |

## 🔧 功能操作

### 补充信息管理
- **自动保存**：输入时实时保存到本地存储
- **状态显示**：显示"已保存X个字段，Y个字符"
- **清空功能**：点击"清空补充信息"一键清除
- **持久化**：浏览器重启后数据仍然保留

### 数据预览功能
- **预览合并数据**：点击"预览合并数据"查看完整数据
- **冲突标识**：被AI数据覆盖的补充信息会标记为"被覆盖"
- **分类显示**：
  - 蓝色：AI解析数据
  - 浅蓝色：补充信息数据
  - 绿色：最终合并结果

### 表单填充优化
- **智能判断**：
  - 单一数据源：直接填充
  - 多个数据源：显示预览确认
- **优先级处理**：AI解析数据优先级高于补充信息
- **完整性检查**：填充前验证数据完整性

## 💡 使用技巧

### 1. 最佳实践
- **个人信息**：使用AI解析功能处理护照、身份证等个人基础信息
- **联系信息**：在补充信息中手动输入邮箱、电话等联系方式
- **旅行信息**：在补充信息中输入航班、住宿等旅行相关信息

### 2. 数据格式建议
```
补充信息输入格式示例：
邮箱：<EMAIL>
电话：+60*********
航班号：MH370
到达日期：2024-03-15
离开日期：2024-03-22
住宿类型：Hotel
地址：123 Jalan Bukit Bintang, Kuala Lumpur
州：Kuala Lumpur
邮编：55100
城市：Kuala Lumpur
```

### 3. 数据管理
- **定期清理**：使用"清空补充信息"清理过时数据
- **数据备份**：重要信息建议额外备份
- **格式统一**：保持字段格式的一致性

## 🔍 故障排除

### 常见问题
1. **补充信息未保存**
   - 检查输入格式是否正确
   - 确认字段名称使用中文冒号

2. **数据合并异常**
   - 检查两个输入源是否都有数据
   - 确认字段名称匹配

3. **表单填充失败**
   - 确认在MDAC网站页面上操作
   - 检查网络连接和权限设置

### 技术支持
- 查看浏览器控制台错误信息
- 检查Chrome扩展权限设置
- 确认AI API配置正确

## 🎉 功能优势

1. **数据完整性**：双输入源确保数据更完整
2. **使用灵活性**：可单独使用任一数据源
3. **持久化存储**：补充信息自动保存，提升效率
4. **智能合并**：自动处理数据冲突和优先级
5. **用户友好**：直观的预览界面和状态反馈

通过双输入源功能，您可以更高效、更准确地完成MDAC表单填充，享受AI智能化与人工精确性的完美结合！
