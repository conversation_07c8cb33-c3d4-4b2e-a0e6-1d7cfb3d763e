/**
 * 马来西亚州属城市数据库使用示例
 * 展示如何使用完整的州属城市数据进行智能匹配和验证
 */

// 使用示例类
class MalaysiaDataExample {
    constructor() {
        this.validator = null;
        this.data = null;
    }

    /**
     * 初始化数据和验证器
     */
    async initialize() {
        try {
            // 加载完整的马来西亚数据
            const response = await fetch('config/malaysia-states-cities.json');
            this.data = await response.json();
            
            // 初始化验证器
            this.validator = new MDACValidator();
            await this.validator.loadCodeMappings();
            
            console.log('✅ 马来西亚数据库初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 初始化失败:', error);
            return false;
        }
    }

    /**
     * 示例1: 基础州属匹配
     */
    demonstrateStateMatching() {
        console.log('\n=== 州属匹配示例 ===');
        
        const testCases = [
            '柔佛',           // 中文
            'Johor',         // 英文
            'JOHOR',         // 大写
            'johor',         // 小写
            '01',            // 代码
            '吉隆坡',        // 中文
            'Kuala Lumpur',  // 英文
            'KL',            // 缩写
            '槟城',          // 中文
            'Penang',        // 英文
            'Pulau Pinang'   // 正式名称
        ];

        testCases.forEach(testCase => {
            const result = this.validator.smartStateMapping(testCase);
            const stateName = result ? this.data.states[result] : '未找到';
            console.log(`输入: "${testCase}" → 代码: ${result} (${stateName})`);
        });
    }

    /**
     * 示例2: 城市智能匹配
     */
    demonstrateCityMatching() {
        console.log('\n=== 城市匹配示例 ===');
        
        const testCases = [
            { city: '新山', state: null },
            { city: 'Johor Bahru', state: null },
            { city: '乔治市', state: '07' },
            { city: 'George Town', state: '07' },
            { city: '吉隆坡', state: null },
            { city: 'KLCC', state: '14' },
            { city: '亚庇', state: '12' },
            { city: 'Kota Kinabalu', state: '12' },
            { city: '古晋', state: '13' },
            { city: 'Kuching', state: '13' }
        ];

        testCases.forEach(({ city, state }) => {
            const result = this.validator.smartCityMapping(city, state);
            if (result) {
                const cityInfo = this.findCityInfo(result);
                console.log(`输入: "${city}" (州属: ${state || '任意'}) → 代码: ${result}`);
                console.log(`  城市: ${cityInfo.name} (${cityInfo.chinese})`);
                console.log(`  州属: ${cityInfo.stateName}`);
                console.log(`  邮编: ${cityInfo.postcode}`);
            } else {
                console.log(`输入: "${city}" → 未找到匹配城市`);
            }
        });
    }

    /**
     * 示例3: 邮政编码匹配
     */
    demonstratePostcodeMatching() {
        console.log('\n=== 邮政编码匹配示例 ===');
        
        const postcodes = [
            '50000',  // 吉隆坡
            '81300',  // 士古来
            '10000',  // 乔治市
            '79000',  // 新山
            '75000',  // 马六甲
            '88000',  // 亚庇
            '93000',  // 古晋
            '25000',  // 关丹
            '05000',  // 亚罗士打
            '15000'   // 哥打巴鲁
        ];

        postcodes.forEach(postcode => {
            const result = this.validator.getCityByPostcode(postcode);
            if (result) {
                const cityInfo = this.findCityInfo(result);
                console.log(`邮编: ${postcode} → 城市: ${cityInfo.name} (${cityInfo.chinese})`);
                console.log(`  州属: ${cityInfo.stateName}`);
            } else {
                console.log(`邮编: ${postcode} → 未找到对应城市`);
            }
        });
    }

    /**
     * 示例4: 获取州属城市列表
     */
    demonstrateStateCities() {
        console.log('\n=== 州属城市列表示例 ===');
        
        const selectedStates = ['01', '07', '14'];
        
        selectedStates.forEach(stateCode => {
            const cities = this.validator.getCitiesByState(stateCode);
            const stateName = this.data.states[stateCode];
            
            console.log(`\n${stateName} (${stateCode}) - 共 ${cities.length} 个城市:`);
            cities.slice(0, 5).forEach(city => {
                console.log(`  ${city.code} - ${city.name} (${city.chinese})`);
            });
            if (cities.length > 5) {
                console.log(`  ... 还有 ${cities.length - 5} 个城市`);
            }
        });
    }

    /**
     * 示例5: 热门旅游目的地
     */
    demonstratePopularDestinations() {
        console.log('\n=== 热门旅游目的地示例 ===');
        
        const popular = this.validator.getPopularDestinations();
        
        console.log(`共 ${popular.length} 个热门目的地:`);
        popular.forEach((destination, index) => {
            console.log(`${index + 1}. ${destination.name} (${destination.chinese})`);
            console.log(`   代码: ${destination.code}, 特色: ${destination.reason}`);
        });
    }

    /**
     * 示例6: 数据验证和匹配
     */
    demonstrateValidation() {
        console.log('\n=== 数据验证示例 ===');
        
        const testPairs = [
            { city: '0100', state: '01' },  // 新山 → 柔佛 (正确)
            { city: '0700', state: '07' },  // 乔治市 → 槟城 (正确)
            { city: '1400', state: '14' },  // 吉隆坡 → 吉隆坡 (正确)
            { city: '0100', state: '07' },  // 新山 → 槟城 (错误)
            { city: '0700', state: '01' }   // 乔治市 → 柔佛 (错误)
        ];

        testPairs.forEach(({ city, state }) => {
            const isValid = this.validator.validateCityStateMatch(city, state);
            const cityInfo = this.findCityInfo(city);
            const stateName = this.data.states[state];
            
            const status = isValid ? '✅ 匹配' : '❌ 不匹配';
            console.log(`${status}: ${cityInfo.name} (${city}) 属于 ${stateName} (${state})`);
        });
    }

    /**
     * 示例7: 模糊匹配和容错
     */
    demonstrateFuzzyMatching() {
        console.log('\n=== 模糊匹配示例 ===');
        
        const fuzzyInputs = [
            'johor bahru',      // 小写
            'GEORGE TOWN',      // 大写
            'kuala lumpur',     // 小写
            'JB',               // 缩写 (可能不支持)
            'KL',               // 缩写
            'ipoh',             // 小写
            'penang',           // 英文别名
            'melaka',           // 英文拼写
            '新山乐高',          // 包含关键词
            '吉隆坡双峰塔'       // 包含关键词
        ];

        fuzzyInputs.forEach(input => {
            // 尝试州属匹配
            const stateResult = this.validator.smartStateMapping(input);
            if (stateResult) {
                console.log(`州属模糊匹配: "${input}" → ${this.data.states[stateResult]} (${stateResult})`);
                return;
            }

            // 尝试城市匹配
            const cityResult = this.validator.smartCityMapping(input);
            if (cityResult) {
                const cityInfo = this.findCityInfo(cityResult);
                console.log(`城市模糊匹配: "${input}" → ${cityInfo.name} (${cityInfo.chinese})`);
            } else {
                console.log(`无法匹配: "${input}"`);
            }
        });
    }

    /**
     * 示例8: 实际表单填充场景
     */
    demonstrateFormFillScenario() {
        console.log('\n=== 表单填充场景示例 ===');
        
        const userInputs = [
            {
                address: '柔佛新山某某路123号',
                state: '柔佛',
                city: '新山',
                postcode: '81300'
            },
            {
                address: 'KLCC Tower, Kuala Lumpur',
                state: 'Kuala Lumpur',
                city: 'KLCC',
                postcode: '50088'
            },
            {
                address: '槟城乔治市某某街456号',
                state: '槟城',
                city: '乔治市',
                postcode: '10000'
            }
        ];

        userInputs.forEach((input, index) => {
            console.log(`\n场景 ${index + 1}: ${input.address}`);
            
            // 处理州属
            const stateCode = this.validator.smartStateMapping(input.state);
            console.log(`州属: "${input.state}" → ${stateCode} (${this.data.states[stateCode]})`);
            
            // 处理城市
            const cityCode = this.validator.smartCityMapping(input.city, stateCode);
            if (cityCode) {
                const cityInfo = this.findCityInfo(cityCode);
                console.log(`城市: "${input.city}" → ${cityCode} (${cityInfo.name})`);
                
                // 验证城市州属匹配
                const isValid = this.validator.validateCityStateMatch(cityCode, stateCode);
                console.log(`匹配验证: ${isValid ? '✅ 通过' : '❌ 失败'}`);
            }
            
            // 处理邮政编码
            const postcodeCity = this.validator.getCityByPostcode(input.postcode);
            if (postcodeCity) {
                const postcodeCityInfo = this.findCityInfo(postcodeCity);
                console.log(`邮编验证: ${input.postcode} → ${postcodeCityInfo.name}`);
                
                // 检查邮编城市是否与用户输入城市一致
                const postcodeMatch = postcodeCity === cityCode;
                console.log(`邮编城市匹配: ${postcodeMatch ? '✅ 一致' : '⚠️ 不一致'}`);
            }
        });
    }

    /**
     * 工具方法: 查找城市完整信息
     */
    findCityInfo(cityCode) {
        for (const [stateCode, stateInfo] of Object.entries(this.data.cities)) {
            if (stateInfo.cities[cityCode]) {
                return {
                    ...stateInfo.cities[cityCode],
                    stateCode: stateCode,
                    stateName: this.data.states[stateCode]
                };
            }
        }
        return null;
    }

    /**
     * 运行所有示例
     */
    async runAllExamples() {
        console.log('🇲🇾 马来西亚州属城市数据库使用示例\n');
        
        const initialized = await this.initialize();
        if (!initialized) {
            console.error('初始化失败，无法运行示例');
            return;
        }

        // 运行所有示例
        this.demonstrateStateMatching();
        this.demonstrateCityMatching();
        this.demonstratePostcodeMatching();
        this.demonstrateStateCities();
        this.demonstratePopularDestinations();
        this.demonstrateValidation();
        this.demonstrateFuzzyMatching();
        this.demonstrateFormFillScenario();
        
        console.log('\n✅ 所有示例运行完成');
    }
}

// 使用示例
async function runExamples() {
    const example = new MalaysiaDataExample();
    await example.runAllExamples();
}

// 如果在浏览器环境中
if (typeof window !== 'undefined') {
    window.MalaysiaDataExample = MalaysiaDataExample;
    window.runMalaysiaExamples = runExamples;
}

// 如果在Node.js环境中
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MalaysiaDataExample;
}
