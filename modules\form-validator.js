/**
 * MDAC AI智能分析工具 - 增强表单验证器
 * 确保所有填入MDAC表单的内容严格符合英文/马来文要求和格式规范
 */

class FormValidator {
    constructor() {
        // MDAC字段规格定义
        this.fieldSpecs = {
            // 个人信息字段
            name: {
                type: 'text',
                maxLength: 50,
                pattern: /^[A-Za-z\s\.\,\-\']+$/,
                required: true,
                description: '英文全名，只允许字母、空格、点号、逗号、连字符、撇号'
            },
            passNo: {
                type: 'text',
                maxLength: 15,
                pattern: /^[A-Z]{1,2}\d{6,9}$/,
                required: true,
                description: '护照号码，1-2个大写字母+6-9位数字'
            },
            dob: {
                type: 'date',
                pattern: /^\d{2}\/\d{2}\/\d{4}$/,
                required: true,
                description: '出生日期，严格DD/MM/YYYY格式'
            },
            nationality: {
                type: 'select',
                pattern: /^[A-Z]{3}$/,
                required: true,
                description: '国籍，3位大写ISO代码'
            },
            sex: {
                type: 'select',
                pattern: /^[12]$/,
                required: true,
                description: '性别，1=男性，2=女性'
            },
            passExpiry: {
                type: 'date',
                pattern: /^\d{2}\/\d{2}\/\d{4}$/,
                required: true,
                description: '护照到期日，严格DD/MM/YYYY格式'
            },
            
            // 联系信息字段
            email: {
                type: 'email',
                maxLength: 100,
                pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                required: true,
                description: '电子邮箱，标准邮箱格式'
            },
            confirmEmail: {
                type: 'email',
                maxLength: 100,
                pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                required: true,
                description: '确认邮箱，必须与email完全一致'
            },
            countryCode: {
                type: 'select',
                pattern: /^\+\d{1,4}$/,
                required: true,
                description: '国家代码，+号加1-4位数字'
            },
            mobileNo: {
                type: 'tel',
                maxLength: 15,
                pattern: /^\d{8,15}$/,
                required: true,
                description: '手机号码，8-15位纯数字，不含国家代码'
            },
            
            // 旅行信息字段
            arrivalDate: {
                type: 'date',
                pattern: /^\d{2}\/\d{2}\/\d{4}$/,
                required: true,
                description: '到达日期，严格DD/MM/YYYY格式'
            },
            departureDate: {
                type: 'date',
                pattern: /^\d{2}\/\d{2}\/\d{4}$/,
                required: true,
                description: '离开日期，严格DD/MM/YYYY格式'
            },
            vesselNm: {
                type: 'text',
                maxLength: 20,
                pattern: /^[A-Z0-9\s\-]+$/,
                required: true,
                description: '航班号/交通工具，大写字母、数字、空格、连字符'
            },
            trvlMode: {
                type: 'select',
                pattern: /^(AIR|LAND|SEA)$/,
                required: true,
                description: '旅行方式，AIR=航空，LAND=陆路，SEA=海路'
            },
            embark: {
                type: 'select',
                pattern: /^[A-Z]{3}$/,
                required: true,
                description: '最后港口，3位大写机场/港口代码'
            },
            
            // 住宿信息字段
            accommodationStay: {
                type: 'select',
                pattern: /^(01|02|03|04|99)$/,
                required: true,
                description: '住宿类型，01=酒店，02=朋友家，03=民宿，04=亲戚家，99=其他'
            },
            accommodationAddress1: {
                type: 'text',
                maxLength: 100,
                pattern: /^[A-Za-z0-9\s\.\,\-\#\/]+$/,
                required: true,
                description: '地址行1，英文，字母、数字、空格、标点符号'
            },
            accommodationAddress2: {
                type: 'text',
                maxLength: 100,
                pattern: /^[A-Za-z0-9\s\.\,\-\#\/]*$/,
                required: false,
                description: '地址行2，英文，可选'
            },
            accommodationState: {
                type: 'select',
                pattern: /^(01|02|03|04|05|06|07|08|09|10|11|12|13|14|15|16)$/,
                required: true,
                description: '州代码，01-16的两位数字'
            },
            accommodationPostcode: {
                type: 'text',
                maxLength: 5,
                pattern: /^\d{5}$/,
                required: true,
                description: '邮政编码，5位数字'
            },
            accommodationCity: {
                type: 'select',
                pattern: /^\d{4}$/,
                required: true,
                description: '城市代码，4位数字'
            }
        };
        
        // 逻辑关系验证规则
        this.logicRules = [
            {
                name: 'email_confirmation',
                fields: ['email', 'confirmEmail'],
                rule: (data) => data.email === data.confirmEmail,
                message: '确认邮箱必须与邮箱地址完全一致'
            },
            {
                name: 'date_sequence',
                fields: ['dob', 'passExpiry', 'arrivalDate', 'departureDate'],
                rule: (data) => {
                    const dates = {};
                    ['dob', 'passExpiry', 'arrivalDate', 'departureDate'].forEach(field => {
                        if (data[field]) {
                            const [day, month, year] = data[field].split('/');
                            dates[field] = new Date(year, month - 1, day);
                        }
                    });
                    
                    // 出生日期 < 护照到期日期
                    if (dates.dob && dates.passExpiry && dates.dob >= dates.passExpiry) {
                        return false;
                    }
                    
                    // 到达日期 < 离开日期
                    if (dates.arrivalDate && dates.departureDate && dates.arrivalDate >= dates.departureDate) {
                        return false;
                    }
                    
                    return true;
                },
                message: '日期逻辑错误：出生日期必须早于护照到期日期，到达日期必须早于离开日期'
            },
            {
                name: 'state_city_consistency',
                fields: ['accommodationState', 'accommodationCity'],
                rule: (data) => {
                    // 这里应该检查州和城市代码的一致性
                    // 简化版本，实际应该有完整的映射表
                    return true; // 暂时返回true，实际需要实现完整的验证
                },
                message: '州代码和城市代码不匹配'
            }
        ];
        
        // 验证统计
        this.stats = {
            totalValidations: 0,
            passedValidations: 0,
            failedValidations: 0,
            fieldErrors: {},
            logicErrors: {}
        };
    }
    
    /**
     * 验证单个字段
     * @param {string} fieldName - 字段名称
     * @param {any} value - 字段值
     * @returns {Object} 验证结果
     */
    validateField(fieldName, value) {
        const spec = this.fieldSpecs[fieldName];
        if (!spec) {
            return {
                isValid: false,
                error: `未知字段: ${fieldName}`,
                fieldName: fieldName
            };
        }
        
        // 必填字段检查
        if (spec.required && (!value || value.toString().trim() === '')) {
            this.updateFieldErrorStats(fieldName, 'required');
            return {
                isValid: false,
                error: `${fieldName}是必填字段`,
                fieldName: fieldName,
                errorType: 'required'
            };
        }
        
        // 如果字段为空且非必填，则通过验证
        if (!value || value.toString().trim() === '') {
            return {
                isValid: true,
                fieldName: fieldName
            };
        }
        
        const stringValue = value.toString().trim();
        
        // 长度检查
        if (spec.maxLength && stringValue.length > spec.maxLength) {
            this.updateFieldErrorStats(fieldName, 'maxLength');
            return {
                isValid: false,
                error: `${fieldName}长度不能超过${spec.maxLength}个字符`,
                fieldName: fieldName,
                errorType: 'maxLength',
                currentLength: stringValue.length,
                maxLength: spec.maxLength
            };
        }
        
        // 格式检查
        if (spec.pattern && !spec.pattern.test(stringValue)) {
            this.updateFieldErrorStats(fieldName, 'pattern');
            return {
                isValid: false,
                error: `${fieldName}格式不正确: ${spec.description}`,
                fieldName: fieldName,
                errorType: 'pattern',
                pattern: spec.pattern.toString(),
                description: spec.description
            };
        }
        
        // 特殊验证
        const specialValidation = this.performSpecialValidation(fieldName, stringValue);
        if (!specialValidation.isValid) {
            this.updateFieldErrorStats(fieldName, 'special');
            return specialValidation;
        }
        
        return {
            isValid: true,
            fieldName: fieldName,
            value: stringValue
        };
    }
    
    /**
     * 执行特殊验证
     */
    performSpecialValidation(fieldName, value) {
        switch (fieldName) {
            case 'passNo':
                // 护照号码特殊验证
                if (!/^[A-Z]/.test(value)) {
                    return {
                        isValid: false,
                        error: '护照号码必须以大写字母开头',
                        fieldName: fieldName,
                        errorType: 'special'
                    };
                }
                break;
                
            case 'dob':
            case 'passExpiry':
            case 'arrivalDate':
            case 'departureDate':
                // 日期有效性验证
                const [day, month, year] = value.split('/');
                const date = new Date(year, month - 1, day);
                if (date.getDate() != day || date.getMonth() != month - 1 || date.getFullYear() != year) {
                    return {
                        isValid: false,
                        error: `${fieldName}不是有效的日期`,
                        fieldName: fieldName,
                        errorType: 'special'
                    };
                }
                break;
                
            case 'email':
            case 'confirmEmail':
                // 邮箱域名验证
                const domain = value.split('@')[1];
                if (domain && domain.includes('..')) {
                    return {
                        isValid: false,
                        error: '邮箱域名格式不正确',
                        fieldName: fieldName,
                        errorType: 'special'
                    };
                }
                break;
                
            case 'accommodationPostcode':
                // 马来西亚邮编验证
                const postcode = parseInt(value);
                if (postcode < 10000 || postcode > 99999) {
                    return {
                        isValid: false,
                        error: '邮政编码必须是10000-99999之间的5位数字',
                        fieldName: fieldName,
                        errorType: 'special'
                    };
                }
                break;
        }
        
        return { isValid: true };
    }
    
    /**
     * 验证完整表单数据
     * @param {Object} formData - 表单数据
     * @returns {Object} 验证结果
     */
    validateForm(formData) {
        this.stats.totalValidations++;
        
        const results = {
            isValid: true,
            fieldResults: {},
            logicResults: {},
            errors: [],
            warnings: [],
            summary: {
                totalFields: 0,
                validFields: 0,
                invalidFields: 0,
                requiredFields: 0,
                completedRequiredFields: 0
            }
        };
        
        // 验证各个字段
        for (const [fieldName, spec] of Object.entries(this.fieldSpecs)) {
            results.summary.totalFields++;
            if (spec.required) {
                results.summary.requiredFields++;
            }
            
            const fieldResult = this.validateField(fieldName, formData[fieldName]);
            results.fieldResults[fieldName] = fieldResult;
            
            if (fieldResult.isValid) {
                results.summary.validFields++;
                if (spec.required && formData[fieldName]) {
                    results.summary.completedRequiredFields++;
                }
            } else {
                results.summary.invalidFields++;
                results.isValid = false;
                results.errors.push(fieldResult.error);
            }
        }
        
        // 验证逻辑关系
        for (const rule of this.logicRules) {
            const logicResult = this.validateLogicRule(rule, formData);
            results.logicResults[rule.name] = logicResult;
            
            if (!logicResult.isValid) {
                results.isValid = false;
                results.errors.push(logicResult.error);
                this.updateLogicErrorStats(rule.name);
            }
        }
        
        // 生成警告
        this.generateWarnings(formData, results);
        
        // 更新统计
        if (results.isValid) {
            this.stats.passedValidations++;
        } else {
            this.stats.failedValidations++;
        }
        
        return results;
    }
    
    /**
     * 验证逻辑规则
     */
    validateLogicRule(rule, formData) {
        try {
            const isValid = rule.rule(formData);
            return {
                isValid: isValid,
                ruleName: rule.name,
                fields: rule.fields,
                error: isValid ? null : rule.message
            };
        } catch (error) {
            return {
                isValid: false,
                ruleName: rule.name,
                fields: rule.fields,
                error: `逻辑验证错误: ${error.message}`
            };
        }
    }
    
    /**
     * 生成警告信息
     */
    generateWarnings(formData, results) {
        // 检查完整性
        const completionRate = results.summary.completedRequiredFields / results.summary.requiredFields;
        if (completionRate < 1) {
            results.warnings.push(`必填字段完成率: ${(completionRate * 100).toFixed(1)}%`);
        }
        
        // 检查数据质量
        if (formData.name && formData.name.length < 3) {
            results.warnings.push('姓名可能过短，请确认是否为完整姓名');
        }
        
        if (formData.mobileNo && formData.mobileNo.length < 8) {
            results.warnings.push('手机号码可能过短，请确认格式是否正确');
        }
        
        // 检查日期合理性
        if (formData.arrivalDate && formData.departureDate) {
            const arrival = new Date(formData.arrivalDate.split('/').reverse().join('-'));
            const departure = new Date(formData.departureDate.split('/').reverse().join('-'));
            const stayDays = (departure - arrival) / (1000 * 60 * 60 * 24);
            
            if (stayDays > 90) {
                results.warnings.push('停留时间超过90天，请确认是否需要特殊签证');
            }
        }
    }
    
    /**
     * 更新字段错误统计
     */
    updateFieldErrorStats(fieldName, errorType) {
        if (!this.stats.fieldErrors[fieldName]) {
            this.stats.fieldErrors[fieldName] = {};
        }
        if (!this.stats.fieldErrors[fieldName][errorType]) {
            this.stats.fieldErrors[fieldName][errorType] = 0;
        }
        this.stats.fieldErrors[fieldName][errorType]++;
    }
    
    /**
     * 更新逻辑错误统计
     */
    updateLogicErrorStats(ruleName) {
        if (!this.stats.logicErrors[ruleName]) {
            this.stats.logicErrors[ruleName] = 0;
        }
        this.stats.logicErrors[ruleName]++;
    }
    
    /**
     * 获取验证统计信息
     */
    getStats() {
        return {
            ...this.stats,
            successRate: this.stats.totalValidations > 0 ? 
                (this.stats.passedValidations / this.stats.totalValidations * 100).toFixed(2) + '%' : '0%'
        };
    }
    
    /**
     * 获取字段规格信息
     */
    getFieldSpec(fieldName) {
        return this.fieldSpecs[fieldName] || null;
    }
    
    /**
     * 获取所有字段规格
     */
    getAllFieldSpecs() {
        return this.fieldSpecs;
    }
    
    /**
     * 清理和标准化数据
     */
    sanitizeData(formData) {
        const sanitized = {};
        
        for (const [fieldName, value] of Object.entries(formData)) {
            if (value === null || value === undefined) {
                continue;
            }
            
            let cleanValue = value.toString().trim();
            
            // 字段特定的清理
            switch (fieldName) {
                case 'name':
                    // 姓名标准化：首字母大写，移除多余空格
                    cleanValue = cleanValue.replace(/\s+/g, ' ')
                        .split(' ')
                        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                        .join(' ');
                    break;
                    
                case 'passNo':
                    // 护照号码：转大写，移除空格和特殊字符
                    cleanValue = cleanValue.toUpperCase().replace(/[^A-Z0-9]/g, '');
                    break;
                    
                case 'email':
                case 'confirmEmail':
                    // 邮箱：转小写
                    cleanValue = cleanValue.toLowerCase();
                    break;
                    
                case 'nationality':
                case 'embark':
                    // 代码字段：转大写
                    cleanValue = cleanValue.toUpperCase();
                    break;
                    
                case 'mobileNo':
                    // 手机号：只保留数字
                    cleanValue = cleanValue.replace(/\D/g, '');
                    break;
                    
                case 'accommodationAddress1':
                case 'accommodationAddress2':
                    // 地址：首字母大写
                    cleanValue = cleanValue.replace(/\b\w/g, l => l.toUpperCase());
                    break;
            }
            
            sanitized[fieldName] = cleanValue;
        }
        
        return sanitized;
    }
}

// 导出类
if (typeof window !== 'undefined') {
    window.FormValidator = FormValidator;
} else if (typeof module !== 'undefined' && module.exports) {
    module.exports = FormValidator;
}
