/**
 * MDAC AI智能分析工具 - 优化测试验证器
 * 全面测试AI识别、表单填充、地址标准化等功能的准确率和性能
 */

class OptimizationTester {
    constructor() {
        // 测试数据集
        this.testDatasets = {
            // 个人信息测试数据
            personalInfo: [
                {
                    input: "姓名：张伟，护照号：A1234567，出生日期：1990年5月15日，国籍：中国，性别：男，护照到期：2025/12/31，邮箱：<EMAIL>，手机：+86 13812345678",
                    expected: {
                        name: "<PERSON>",
                        passportNo: "A1234567",
                        dateOfBirth: "15/05/1990",
                        nationality: "CHN",
                        sex: "1",
                        passportExpiry: "31/12/2025",
                        email: "<EMAIL>",
                        countryCode: "+86",
                        mobileNo: "13812345678"
                    }
                },
                {
                    input: "Name: <PERSON>, Passport: B9876543, DOB: 25-03-1985, Nationality: USA, Gender: Male, Expiry: 15.08.2026, Email: <EMAIL>, Phone: ******-123-4567",
                    expected: {
                        name: "<PERSON>",
                        passportNo: "B9876543",
                        dateOfBirth: "25/03/1985",
                        nationality: "USA",
                        sex: "1",
                        passportExpiry: "15/08/2026",
                        email: "<EMAIL>",
                        countryCode: "+1",
                        mobileNo: "5551234567"
                    }
                }
            ],
            
            // 旅行信息测试数据
            travelInfo: [
                {
                    input: "到达日期：2024年1月15日，离开日期：2024年1月25日，航班：MH123，交通方式：飞机，出发地：北京首都机场，住宿：酒店，地址：吉隆坡市中心武吉免登路123号",
                    expected: {
                        arrivalDate: "15/01/2024",
                        departureDate: "25/01/2024",
                        flightNo: "MH123",
                        modeOfTravel: "AIR",
                        lastPort: "PEK",
                        accommodation: "01",
                        address: "123 Jalan Bukit Bintang",
                        state: "14",
                        city: "1400"
                    }
                }
            ],
            
            // 地址标准化测试数据
            addresses: [
                {
                    input: "吉隆坡市中心武吉免登路123号",
                    expected: {
                        standardized: "123 Jalan Bukit Bintang, Kuala Lumpur",
                        state: "14",
                        city: "1400",
                        postcode: "50200"
                    }
                },
                {
                    input: "新山市士古来花园住宅区第5街道88号",
                    expected: {
                        standardized: "88 Jalan 5, Taman Skudai, Johor Bahru",
                        state: "01",
                        city: "0142",
                        postcode: "81300"
                    }
                }
            ],
            
            // 日期格式测试数据
            dates: [
                { input: "1990年5月15日", expected: "15/05/1990", context: "birth" },
                { input: "25-03-1985", expected: "25/03/1985", context: "birth" },
                { input: "2025/12/31", expected: "31/12/2025", context: "expiry" },
                { input: "15.08.2026", expected: "15/08/2026", context: "expiry" },
                { input: "今天", expected: this.getTodayFormatted(), context: "arrival" },
                { input: "明天", expected: this.getTomorrowFormatted(), context: "departure" }
            ]
        };
        
        // 测试结果统计
        this.testResults = {
            personalInfo: { total: 0, passed: 0, failed: 0, accuracy: 0 },
            travelInfo: { total: 0, passed: 0, failed: 0, accuracy: 0 },
            addresses: { total: 0, passed: 0, failed: 0, accuracy: 0 },
            dates: { total: 0, passed: 0, failed: 0, accuracy: 0 },
            overall: { total: 0, passed: 0, failed: 0, accuracy: 0 }
        };
        
        // 性能测试结果
        this.performanceResults = {
            aiProcessing: { min: 0, max: 0, avg: 0, samples: [] },
            formValidation: { min: 0, max: 0, avg: 0, samples: [] },
            addressStandardization: { min: 0, max: 0, avg: 0, samples: [] },
            overall: { min: 0, max: 0, avg: 0, samples: [] }
        };
        
        // 错误分析
        this.errorAnalysis = {
            fieldMismatches: {},
            formatErrors: {},
            logicErrors: {},
            performanceIssues: []
        };
    }
    
    /**
     * 运行完整的优化测试套件
     */
    async runFullTestSuite() {
        console.log('🧪 开始MDAC AI优化测试套件...');
        
        const startTime = Date.now();
        
        try {
            // 1. 测试AI识别逻辑
            console.log('📝 测试AI识别逻辑...');
            await this.testAIRecognition();
            
            // 2. 测试表单验证
            console.log('✅ 测试表单验证逻辑...');
            await this.testFormValidation();
            
            // 3. 测试日期格式化
            console.log('📅 测试日期格式化...');
            await this.testDateFormatting();
            
            // 4. 测试地址标准化
            console.log('🗺️ 测试地址标准化...');
            await this.testAddressStandardization();
            
            // 5. 性能基准测试
            console.log('⚡ 运行性能基准测试...');
            await this.runPerformanceBenchmarks();
            
            // 6. 生成测试报告
            console.log('📊 生成测试报告...');
            const report = this.generateTestReport();
            
            const totalTime = Date.now() - startTime;
            console.log(`✅ 测试套件完成，总耗时: ${totalTime}ms`);
            
            return report;
            
        } catch (error) {
            console.error('❌ 测试套件执行失败:', error);
            return {
                success: false,
                error: error.message,
                partialResults: this.testResults
            };
        }
    }
    
    /**
     * 测试AI识别逻辑
     */
    async testAIRecognition() {
        // 测试个人信息识别
        for (const testCase of this.testDatasets.personalInfo) {
            this.testResults.personalInfo.total++;
            
            const startTime = Date.now();
            
            try {
                // 这里应该调用实际的AI识别函数
                // const result = await aiRecognitionFunction(testCase.input);
                
                // 模拟AI识别结果（实际应该调用真实的AI函数）
                const result = this.simulateAIRecognition(testCase.input, 'personal');
                
                const processingTime = Date.now() - startTime;
                this.recordPerformance('aiProcessing', processingTime);
                
                // 验证结果
                const isValid = this.validateAIResult(result, testCase.expected);
                
                if (isValid) {
                    this.testResults.personalInfo.passed++;
                } else {
                    this.testResults.personalInfo.failed++;
                    this.recordError('personalInfo', testCase, result);
                }
                
            } catch (error) {
                this.testResults.personalInfo.failed++;
                this.recordError('personalInfo', testCase, null, error.message);
            }
        }
        
        // 计算准确率
        this.testResults.personalInfo.accuracy = 
            (this.testResults.personalInfo.passed / this.testResults.personalInfo.total * 100).toFixed(2);
        
        // 测试旅行信息识别
        for (const testCase of this.testDatasets.travelInfo) {
            this.testResults.travelInfo.total++;
            
            try {
                const result = this.simulateAIRecognition(testCase.input, 'travel');
                const isValid = this.validateAIResult(result, testCase.expected);
                
                if (isValid) {
                    this.testResults.travelInfo.passed++;
                } else {
                    this.testResults.travelInfo.failed++;
                    this.recordError('travelInfo', testCase, result);
                }
                
            } catch (error) {
                this.testResults.travelInfo.failed++;
                this.recordError('travelInfo', testCase, null, error.message);
            }
        }
        
        this.testResults.travelInfo.accuracy = 
            (this.testResults.travelInfo.passed / this.testResults.travelInfo.total * 100).toFixed(2);
    }
    
    /**
     * 测试表单验证
     */
    async testFormValidation() {
        if (typeof FormValidator === 'undefined') {
            console.warn('FormValidator未加载，跳过表单验证测试');
            return;
        }
        
        const validator = new FormValidator();
        
        // 测试各种验证场景
        const validationTests = [
            { field: 'name', value: 'Zhang Wei', shouldPass: true },
            { field: 'name', value: '张伟123', shouldPass: false },
            { field: 'passNo', value: 'A1234567', shouldPass: true },
            { field: 'passNo', value: 'a1234567', shouldPass: false },
            { field: 'email', value: '<EMAIL>', shouldPass: true },
            { field: 'email', value: 'invalid-email', shouldPass: false },
            { field: 'dob', value: '15/05/1990', shouldPass: true },
            { field: 'dob', value: '32/13/1990', shouldPass: false }
        ];
        
        for (const test of validationTests) {
            const startTime = Date.now();
            const result = validator.validateField(test.field, test.value);
            const processingTime = Date.now() - startTime;
            
            this.recordPerformance('formValidation', processingTime);
            
            const passed = (result.isValid === test.shouldPass);
            if (!passed) {
                this.recordError('formValidation', test, result);
            }
        }
    }
    
    /**
     * 测试日期格式化
     */
    async testDateFormatting() {
        if (typeof DateFormatter === 'undefined') {
            console.warn('DateFormatter未加载，跳过日期格式化测试');
            return;
        }
        
        const formatter = new DateFormatter();
        
        for (const testCase of this.testDatasets.dates) {
            this.testResults.dates.total++;
            
            const startTime = Date.now();
            const result = formatter.parseDate(testCase.input, testCase.context);
            const processingTime = Date.now() - startTime;
            
            this.recordPerformance('aiProcessing', processingTime);
            
            if (result.success && result.formatted === testCase.expected) {
                this.testResults.dates.passed++;
            } else {
                this.testResults.dates.failed++;
                this.recordError('dates', testCase, result);
            }
        }
        
        this.testResults.dates.accuracy = 
            (this.testResults.dates.passed / this.testResults.dates.total * 100).toFixed(2);
    }
    
    /**
     * 测试地址标准化
     */
    async testAddressStandardization() {
        if (typeof GoogleMapsIntegration === 'undefined') {
            console.warn('GoogleMapsIntegration未加载，跳过地址标准化测试');
            return;
        }
        
        // 注意：这里需要有效的API密钥才能进行真实测试
        // 在没有API密钥的情况下，我们进行模拟测试
        
        for (const testCase of this.testDatasets.addresses) {
            this.testResults.addresses.total++;
            
            try {
                // 模拟地址标准化结果
                const result = this.simulateAddressStandardization(testCase.input);
                
                const isValid = this.validateAddressResult(result, testCase.expected);
                
                if (isValid) {
                    this.testResults.addresses.passed++;
                } else {
                    this.testResults.addresses.failed++;
                    this.recordError('addresses', testCase, result);
                }
                
            } catch (error) {
                this.testResults.addresses.failed++;
                this.recordError('addresses', testCase, null, error.message);
            }
        }
        
        this.testResults.addresses.accuracy = 
            (this.testResults.addresses.passed / this.testResults.addresses.total * 100).toFixed(2);
    }
    
    /**
     * 运行性能基准测试
     */
    async runPerformanceBenchmarks() {
        const iterations = 10;
        
        // AI处理性能测试
        for (let i = 0; i < iterations; i++) {
            const startTime = Date.now();
            this.simulateAIRecognition("测试数据", "personal");
            const processingTime = Date.now() - startTime;
            this.recordPerformance('aiProcessing', processingTime);
        }
        
        // 表单验证性能测试
        if (typeof FormValidator !== 'undefined') {
            const validator = new FormValidator();
            for (let i = 0; i < iterations; i++) {
                const startTime = Date.now();
                validator.validateField('name', 'Test Name');
                const processingTime = Date.now() - startTime;
                this.recordPerformance('formValidation', processingTime);
            }
        }
        
        // 计算性能统计
        this.calculatePerformanceStats();
    }
    
    /**
     * 模拟AI识别（实际应该调用真实的AI函数）
     */
    simulateAIRecognition(input, type) {
        // 这是模拟函数，实际应该调用真实的AI识别逻辑
        if (type === 'personal') {
            return {
                name: "Zhang Wei",
                passportNo: "A1234567",
                dateOfBirth: "15/05/1990",
                nationality: "CHN",
                sex: "1"
            };
        } else if (type === 'travel') {
            return {
                arrivalDate: "15/01/2024",
                departureDate: "25/01/2024",
                flightNo: "MH123",
                modeOfTravel: "AIR"
            };
        }
        return {};
    }
    
    /**
     * 模拟地址标准化
     */
    simulateAddressStandardization(address) {
        return {
            success: true,
            standardizedAddress: "123 Jalan Bukit Bintang, Kuala Lumpur",
            mdacMapping: {
                address: "123 Jalan Bukit Bintang",
                state: "14",
                city: "1400",
                postcode: "50200"
            }
        };
    }
    
    /**
     * 验证AI识别结果
     */
    validateAIResult(actual, expected) {
        for (const [key, expectedValue] of Object.entries(expected)) {
            if (actual[key] !== expectedValue) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 验证地址标准化结果
     */
    validateAddressResult(actual, expected) {
        if (!actual.success) return false;
        
        const mapping = actual.mdacMapping;
        return mapping.state === expected.state && 
               mapping.city === expected.city;
    }
    
    /**
     * 记录性能数据
     */
    recordPerformance(category, time) {
        if (!this.performanceResults[category]) {
            this.performanceResults[category] = { min: 0, max: 0, avg: 0, samples: [] };
        }
        
        this.performanceResults[category].samples.push(time);
    }
    
    /**
     * 计算性能统计
     */
    calculatePerformanceStats() {
        for (const [category, data] of Object.entries(this.performanceResults)) {
            if (data.samples.length > 0) {
                data.min = Math.min(...data.samples);
                data.max = Math.max(...data.samples);
                data.avg = Math.round(data.samples.reduce((a, b) => a + b, 0) / data.samples.length);
            }
        }
    }
    
    /**
     * 记录错误
     */
    recordError(category, testCase, result, error = null) {
        if (!this.errorAnalysis[category]) {
            this.errorAnalysis[category] = [];
        }
        
        this.errorAnalysis[category].push({
            input: testCase.input || testCase,
            expected: testCase.expected,
            actual: result,
            error: error,
            timestamp: new Date().toISOString()
        });
    }
    
    /**
     * 生成测试报告
     */
    generateTestReport() {
        // 计算总体统计
        const totalTests = Object.values(this.testResults).reduce((sum, cat) => sum + cat.total, 0);
        const totalPassed = Object.values(this.testResults).reduce((sum, cat) => sum + cat.passed, 0);
        const overallAccuracy = totalTests > 0 ? (totalPassed / totalTests * 100).toFixed(2) : '0';
        
        return {
            summary: {
                totalTests: totalTests,
                totalPassed: totalPassed,
                totalFailed: totalTests - totalPassed,
                overallAccuracy: overallAccuracy + '%',
                testDate: new Date().toISOString()
            },
            categoryResults: this.testResults,
            performanceResults: this.performanceResults,
            errorAnalysis: this.errorAnalysis,
            recommendations: this.generateRecommendations()
        };
    }
    
    /**
     * 生成优化建议
     */
    generateRecommendations() {
        const recommendations = [];
        
        // 基于准确率的建议
        for (const [category, result] of Object.entries(this.testResults)) {
            if (result.total > 0) {
                const accuracy = parseFloat(result.accuracy);
                if (accuracy < 90) {
                    recommendations.push(`${category}识别准确率为${accuracy}%，建议优化AI提示词和识别逻辑`);
                }
            }
        }
        
        // 基于性能的建议
        for (const [category, perf] of Object.entries(this.performanceResults)) {
            if (perf.avg > 2000) {
                recommendations.push(`${category}平均响应时间${perf.avg}ms过长，建议优化性能`);
            }
        }
        
        return recommendations;
    }
    
    /**
     * 获取今天的格式化日期
     */
    getTodayFormatted() {
        const today = new Date();
        const day = String(today.getDate()).padStart(2, '0');
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const year = today.getFullYear();
        return `${day}/${month}/${year}`;
    }
    
    /**
     * 获取明天的格式化日期
     */
    getTomorrowFormatted() {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const day = String(tomorrow.getDate()).padStart(2, '0');
        const month = String(tomorrow.getMonth() + 1).padStart(2, '0');
        const year = tomorrow.getFullYear();
        return `${day}/${month}/${year}`;
    }
}

// 导出类
if (typeof window !== 'undefined') {
    window.OptimizationTester = OptimizationTester;
} else if (typeof module !== 'undefined' && module.exports) {
    module.exports = OptimizationTester;
}
