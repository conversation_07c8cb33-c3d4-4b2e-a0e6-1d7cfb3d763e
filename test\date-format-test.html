<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期格式转换测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        .test-case {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #2563eb;
        }
        .input-output {
            display: flex;
            gap: 20px;
            align-items: center;
            margin: 10px 0;
        }
        .input-date, .output-date {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-family: monospace;
        }
        .input-date {
            background: #fef3c7;
        }
        .output-date {
            background: #d1fae5;
        }
        .arrow {
            font-size: 18px;
            color: #6b7280;
        }
        .test-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #1d4ed8;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        .error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        .log {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 MDAC AI日期格式转换测试</h1>
    
    <div class="test-section">
        <h2>📋 测试说明</h2>
        <p>本页面用于测试MDAC AI Chrome扩展中的日期格式转换功能。该功能将AI解析返回的各种日期格式转换为HTML date输入字段要求的 yyyy-MM-dd 格式。</p>
    </div>

    <div class="test-section">
        <h2>🔧 支持的日期格式</h2>
        
        <div class="test-case">
            <h3>dd/MM/yyyy 格式</h3>
            <div class="input-output">
                <span class="input-date">12/07/2025</span>
                <span class="arrow">→</span>
                <span class="output-date">2025-07-12</span>
            </div>
            <div class="input-output">
                <span class="input-date">15/03/1990</span>
                <span class="arrow">→</span>
                <span class="output-date">1990-03-15</span>
            </div>
        </div>

        <div class="test-case">
            <h3>dd-MM-yyyy 格式</h3>
            <div class="input-output">
                <span class="input-date">17-07-2025</span>
                <span class="arrow">→</span>
                <span class="output-date">2025-07-17</span>
            </div>
        </div>

        <div class="test-case">
            <h3>dd.MM.yyyy 格式</h3>
            <div class="input-output">
                <span class="input-date">25.12.1985</span>
                <span class="arrow">→</span>
                <span class="output-date">1985-12-25</span>
            </div>
        </div>

        <div class="test-case">
            <h3>yyyy-MM-dd 格式（已是目标格式）</h3>
            <div class="input-output">
                <span class="input-date">2025-07-12</span>
                <span class="arrow">→</span>
                <span class="output-date">2025-07-12</span>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 实际测试</h2>
        
        <div>
            <label for="testInput">输入日期字符串:</label>
            <input type="text" id="testInput" placeholder="例如: 12/07/2025" style="margin: 10px; padding: 8px; width: 200px;">
            <button class="test-button" onclick="testDateConversion()">转换测试</button>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <button class="test-button" onclick="clearResults()">清除结果</button>
        </div>

        <div id="testResults"></div>
        <div id="testLog" class="log" style="margin-top: 20px; display: none;"></div>
    </div>

    <div class="test-section">
        <h2>📝 测试用例</h2>
        <p>点击下面的测试用例快速填入测试输入框：</p>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
            <button class="test-button" onclick="setTestInput('12/07/2025')">12/07/2025</button>
            <button class="test-button" onclick="setTestInput('17/07/2025')">17/07/2025</button>
            <button class="test-button" onclick="setTestInput('15/03/1990')">15/03/1990</button>
            <button class="test-button" onclick="setTestInput('25-12-1985')">25-12-1985</button>
            <button class="test-button" onclick="setTestInput('01.01.2000')">01.01.2000</button>
            <button class="test-button" onclick="setTestInput('2025-07-12')">2025-07-12</button>
            <button class="test-button" onclick="setTestInput('invalid-date')">invalid-date</button>
            <button class="test-button" onclick="setTestInput('32/13/2025')">32/13/2025</button>
        </div>
    </div>

    <script>
        // 模拟MDAC AI的日期转换函数
        function convertDateFormat(dateStr) {
            if (!dateStr || typeof dateStr !== 'string') {
                console.warn('⚠️ 日期转换：输入为空或非字符串:', dateStr);
                return dateStr;
            }

            const originalDate = dateStr.trim();
            console.log(`📅 开始日期格式转换: "${originalDate}"`);

            try {
                // 支持的日期格式模式
                const patterns = [
                    // dd/MM/yyyy 格式
                    {
                        regex: /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
                        format: 'dd/MM/yyyy'
                    },
                    // dd-MM-yyyy 格式
                    {
                        regex: /^(\d{1,2})-(\d{1,2})-(\d{4})$/,
                        format: 'dd-MM-yyyy'
                    },
                    // dd.MM.yyyy 格式
                    {
                        regex: /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/,
                        format: 'dd.MM.yyyy'
                    },
                    // yyyy-MM-dd 格式（已经是目标格式）
                    {
                        regex: /^(\d{4})-(\d{1,2})-(\d{1,2})$/,
                        format: 'yyyy-MM-dd',
                        isTarget: true
                    },
                    // yyyy/MM/dd 格式
                    {
                        regex: /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,
                        format: 'yyyy/MM/dd',
                        isTarget: true
                    }
                ];

                for (const pattern of patterns) {
                    const match = originalDate.match(pattern.regex);
                    if (match) {
                        console.log(`📋 匹配到格式: ${pattern.format}`);
                        
                        if (pattern.isTarget) {
                            // 如果已经是目标格式或类似格式，进行标准化
                            const year = match[1];
                            const month = match[2].padStart(2, '0');
                            const day = match[3].padStart(2, '0');
                            const result = `${year}-${month}-${day}`;
                            console.log(`✅ 日期格式转换成功: "${originalDate}" → "${result}"`);
                            return result;
                        } else {
                            // dd/MM/yyyy 类型格式，需要重新排列
                            const day = match[1].padStart(2, '0');
                            const month = match[2].padStart(2, '0');
                            const year = match[3];
                            
                            // 验证日期有效性
                            const date = new Date(year, month - 1, day);
                            if (date.getFullYear() == year && 
                                date.getMonth() == month - 1 && 
                                date.getDate() == day) {
                                const result = `${year}-${month}-${day}`;
                                console.log(`✅ 日期格式转换成功: "${originalDate}" → "${result}"`);
                                return result;
                            } else {
                                console.warn(`⚠️ 日期无效: ${originalDate}`);
                                return originalDate;
                            }
                        }
                    }
                }

                // 如果没有匹配到任何模式，尝试使用Date对象解析
                console.log('🔄 尝试使用Date对象解析');
                const date = new Date(originalDate);
                if (!isNaN(date.getTime())) {
                    const year = date.getFullYear();
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const day = date.getDate().toString().padStart(2, '0');
                    const result = `${year}-${month}-${day}`;
                    console.log(`✅ Date对象解析成功: "${originalDate}" → "${result}"`);
                    return result;
                }

                console.warn(`⚠️ 无法识别的日期格式: "${originalDate}"`);
                return originalDate;

            } catch (error) {
                console.error(`❌ 日期格式转换失败: "${originalDate}"`, error);
                return originalDate;
            }
        }

        function setTestInput(value) {
            document.getElementById('testInput').value = value;
        }

        function testDateConversion() {
            const input = document.getElementById('testInput').value;
            const logDiv = document.getElementById('testLog');
            const resultsDiv = document.getElementById('testResults');
            
            // 清除之前的控制台日志
            console.clear();
            
            // 显示日志区域
            logDiv.style.display = 'block';
            logDiv.textContent = '';
            
            // 重定向console.log到页面
            const originalLog = console.log;
            const originalWarn = console.warn;
            const originalError = console.error;
            
            console.log = (...args) => {
                logDiv.textContent += args.join(' ') + '\n';
                originalLog(...args);
            };
            console.warn = (...args) => {
                logDiv.textContent += args.join(' ') + '\n';
                originalWarn(...args);
            };
            console.error = (...args) => {
                logDiv.textContent += args.join(' ') + '\n';
                originalError(...args);
            };
            
            try {
                const result = convertDateFormat(input);
                const isSuccess = result !== input && /^\d{4}-\d{2}-\d{2}$/.test(result);
                
                const resultDiv = document.createElement('div');
                resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
                resultDiv.innerHTML = `
                    <strong>输入:</strong> ${input}<br>
                    <strong>输出:</strong> ${result}<br>
                    <strong>状态:</strong> ${isSuccess ? '✅ 转换成功' : '⚠️ 转换失败或无需转换'}
                `;
                
                resultsDiv.appendChild(resultDiv);
                
            } catch (error) {
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>输入:</strong> ${input}<br>
                    <strong>错误:</strong> ${error.message}
                `;
                resultsDiv.appendChild(resultDiv);
            }
            
            // 恢复原始console方法
            setTimeout(() => {
                console.log = originalLog;
                console.warn = originalWarn;
                console.error = originalError;
            }, 100);
        }

        function runAllTests() {
            const testCases = [
                '12/07/2025',
                '17/07/2025', 
                '15/03/1990',
                '25-12-1985',
                '01.01.2000',
                '2025-07-12',
                'invalid-date',
                '32/13/2025'
            ];
            
            clearResults();
            
            testCases.forEach(testCase => {
                setTestInput(testCase);
                testDateConversion();
            });
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('testLog').textContent = '';
            document.getElementById('testLog').style.display = 'none';
        }
    </script>
</body>
</html>
