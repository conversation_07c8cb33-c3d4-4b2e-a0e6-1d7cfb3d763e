<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>城市查看器测试</title>
    <link rel="stylesheet" href="../ui/ui-sidepanel.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .test-header {
            background: #007acc;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-content {
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
        }
        .test-title {
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
        }
        .btn {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background: #005a9e;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007acc;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🏙️ 马来西亚城市查看器测试</h1>
            <p>测试城市数据加载、搜索、过滤和显示功能</p>
        </div>

        <div class="test-content">
            <!-- 数据统计 -->
            <div class="test-section">
                <div class="test-title">📊 数据统计</div>
                <div class="stats" id="dataStats">
                    <div class="stat-item">
                        <div class="stat-number" id="totalStates">-</div>
                        <div class="stat-label">州属总数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="totalCities">-</div>
                        <div class="stat-label">城市总数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="popularCities">-</div>
                        <div class="stat-label">热门目的地</div>
                    </div>
                </div>
            </div>

            <!-- 功能测试 -->
            <div class="test-section">
                <div class="test-title">🧪 功能测试</div>
                <button class="btn" onclick="testLoadAllCities()">加载所有城市</button>
                <button class="btn" onclick="testSearchCities()">测试搜索</button>
                <button class="btn" onclick="testStateFilter()">测试州属过滤</button>
                <button class="btn" onclick="testPopularDestinations()">热门目的地</button>
                <button class="btn" onclick="testCityDetails()">城市详情</button>
                <div class="test-result" id="testResult"></div>
            </div>

            <!-- 城市查看器演示 -->
            <div class="test-section">
                <div class="test-title">🎯 城市查看器演示</div>
                <button class="btn" onclick="showCityViewer()">显示城市查看器</button>
                
                <!-- 城市查看器区域 -->
                <div class="city-viewer-area" id="cityViewerArea" style="display: none;">
                    <div class="city-viewer-header">
                        <span class="viewer-icon">🏙️</span>
                        <span class="viewer-title">马来西亚城市查看器</span>
                        <button class="close-viewer-btn" id="closeViewerBtn">&times;</button>
                    </div>
                    
                    <!-- 搜索和过滤 -->
                    <div class="city-search-section">
                        <div class="search-box">
                            <input type="text" id="citySearchInput" class="search-input" placeholder="搜索城市或州属...">
                            <button class="search-btn" id="citySearchBtn">🔍</button>
                        </div>
                        <div class="filter-section">
                            <select id="stateFilter" class="state-filter">
                                <option value="">所有州属</option>
                            </select>
                            <button class="show-popular-btn" id="showPopularBtn">热门目的地</button>
                        </div>
                    </div>

                    <!-- 城市列表 -->
                    <div class="city-list-container">
                        <div class="city-list-header">
                            <div class="list-stats" id="cityListStats">共 0 个城市</div>
                            <div class="view-options">
                                <button class="view-btn active" data-view="list" id="listViewBtn">📋</button>
                                <button class="view-btn" data-view="grid" id="gridViewBtn">📱</button>
                            </div>
                        </div>
                        <div class="city-list" id="cityList">
                            <!-- 城市列表将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="../utils/mdac-validator.js"></script>
    
    <script>
        // 测试变量
        let validator = null;
        let currentCities = [];
        let filteredCities = [];
        let currentViewMode = 'list';

        // 初始化测试
        async function initializeTest() {
            console.log('🚀 初始化城市查看器测试...');
            
            try {
                // 创建验证器实例
                validator = new MDACValidator();
                
                // 等待数据加载
                await waitForDataLoaded();
                
                // 更新统计信息
                updateStats();
                
                // 设置事件监听器
                setupEventListeners();
                
                console.log('✅ 测试初始化完成');
                document.getElementById('testResult').innerHTML = '<span style="color: green;">✅ 初始化完成，数据加载成功</span>';
                
            } catch (error) {
                console.error('❌ 初始化失败:', error);
                document.getElementById('testResult').innerHTML = '<span style="color: red;">❌ 初始化失败: ' + error.message + '</span>';
            }
        }

        // 等待数据加载完成
        function waitForDataLoaded() {
            return new Promise(resolve => {
                const checkDataLoaded = () => {
                    if (validator.codeMappings && validator.codeMappings.cities) {
                        resolve();
                    } else {
                        setTimeout(checkDataLoaded, 100);
                    }
                };
                checkDataLoaded();
            });
        }

        // 更新统计信息
        function updateStats() {
            if (!validator || !validator.codeMappings) return;
            
            const statesCount = Object.keys(validator.codeMappings.states || {}).length;
            const citiesCount = Object.keys(validator.codeMappings.cities || {}).length;
            const popularCount = validator.codeMappings.popularDestinations ? 
                validator.codeMappings.popularDestinations.tourist_cities.length : 0;
            
            document.getElementById('totalStates').textContent = statesCount;
            document.getElementById('totalCities').textContent = citiesCount;
            document.getElementById('popularCities').textContent = popularCount;
        }

        // 设置事件监听器
        function setupEventListeners() {
            document.getElementById('closeViewerBtn')?.addEventListener('click', closeCityViewer);
            document.getElementById('citySearchInput')?.addEventListener('input', handleCitySearch);
            document.getElementById('citySearchBtn')?.addEventListener('click', performCitySearch);
            document.getElementById('stateFilter')?.addEventListener('change', handleStateFilter);
            document.getElementById('showPopularBtn')?.addEventListener('click', showPopularDestinations);
            document.getElementById('listViewBtn')?.addEventListener('click', () => switchView('list'));
            document.getElementById('gridViewBtn')?.addEventListener('click', () => switchView('grid'));
        }

        // 测试函数
        function testLoadAllCities() {
            if (!validator) {
                document.getElementById('testResult').innerHTML = '<span style="color: red;">❌ 验证器未初始化</span>';
                return;
            }
            
            const cities = validator.getAllCitiesWithDetails();
            document.getElementById('testResult').innerHTML = 
                `<span style="color: green;">✅ 成功加载 ${cities.length} 个城市</span><br>
                <details><summary>查看前5个城市</summary><pre>${JSON.stringify(cities.slice(0, 5), null, 2)}</pre></details>`;
        }

        function testSearchCities() {
            if (!validator) return;
            
            const testQueries = ['吉隆坡', 'George', 'Johor', '新山'];
            const results = testQueries.map(query => ({
                query,
                results: validator.searchCities(query, 3)
            }));
            
            document.getElementById('testResult').innerHTML = 
                `<span style="color: green;">✅ 搜索测试完成</span><br>
                <details><summary>查看搜索结果</summary><pre>${JSON.stringify(results, null, 2)}</pre></details>`;
        }

        function testStateFilter() {
            if (!validator) return;
            
            const johorCities = validator.getCitiesByStateWithDetails('01');
            const klCities = validator.getCitiesByStateWithDetails('14');
            
            document.getElementById('testResult').innerHTML = 
                `<span style="color: green;">✅ 州属过滤测试完成</span><br>
                柔佛: ${johorCities.length} 个城市<br>
                吉隆坡: ${klCities.length} 个城市<br>
                <details><summary>查看柔佛城市</summary><pre>${JSON.stringify(johorCities, null, 2)}</pre></details>`;
        }

        function testPopularDestinations() {
            if (!validator) return;
            
            const popular = validator.getPopularDestinationsWithDetails();
            document.getElementById('testResult').innerHTML = 
                `<span style="color: green;">✅ 热门目的地测试完成</span><br>
                找到 ${popular.length} 个热门目的地<br>
                <details><summary>查看热门目的地</summary><pre>${JSON.stringify(popular, null, 2)}</pre></details>`;
        }

        function testCityDetails() {
            if (!validator) return;
            
            const testCodes = ['0100', '1400', '0700'];
            const details = testCodes.map(code => ({
                code,
                details: validator.getCityDetails(code)
            }));
            
            document.getElementById('testResult').innerHTML = 
                `<span style="color: green;">✅ 城市详情测试完成</span><br>
                <details><summary>查看城市详情</summary><pre>${JSON.stringify(details, null, 2)}</pre></details>`;
        }

        // 城市查看器功能
        function showCityViewer() {
            const viewerArea = document.getElementById('cityViewerArea');
            if (viewerArea) {
                viewerArea.style.display = 'block';
                initializeStateFilter();
                loadAllCities();
            }
        }

        function closeCityViewer() {
            const viewerArea = document.getElementById('cityViewerArea');
            if (viewerArea) {
                viewerArea.style.display = 'none';
            }
        }

        function initializeStateFilter() {
            const stateFilter = document.getElementById('stateFilter');
            if (!stateFilter || !validator || !validator.codeMappings.states) return;
            
            stateFilter.innerHTML = '<option value="">所有州属</option>';
            Object.entries(validator.codeMappings.states).forEach(([code, name]) => {
                const option = document.createElement('option');
                option.value = code;
                option.textContent = name;
                stateFilter.appendChild(option);
            });
        }

        function loadAllCities() {
            if (!validator) return;
            
            currentCities = validator.getAllCitiesWithDetails();
            filteredCities = [...currentCities];
            renderCityList();
            updateCityStats();
        }

        function handleCitySearch(event) {
            const query = event.target.value.trim();
            setTimeout(() => performCitySearchWithQuery(query), 300);
        }

        function performCitySearch() {
            const searchInput = document.getElementById('citySearchInput');
            if (searchInput) {
                performCitySearchWithQuery(searchInput.value.trim());
            }
        }

        function performCitySearchWithQuery(query) {
            if (!query) {
                filteredCities = [...currentCities];
            } else {
                filteredCities = validator.searchCities(query, 50);
            }
            renderCityList();
            updateCityStats();
        }

        function handleStateFilter(event) {
            const stateCode = event.target.value;
            if (!stateCode) {
                filteredCities = [...currentCities];
            } else {
                filteredCities = validator.getCitiesByStateWithDetails(stateCode);
            }
            renderCityList();
            updateCityStats();
        }

        function showPopularDestinations() {
            filteredCities = validator.getPopularDestinationsWithDetails();
            renderCityList();
            updateCityStats();
        }

        function switchView(mode) {
            currentViewMode = mode;
            updateViewButtons();
            renderCityList();
        }

        function updateViewButtons() {
            const listBtn = document.getElementById('listViewBtn');
            const gridBtn = document.getElementById('gridViewBtn');
            
            if (listBtn && gridBtn) {
                listBtn.classList.toggle('active', currentViewMode === 'list');
                gridBtn.classList.toggle('active', currentViewMode === 'grid');
            }
        }

        function renderCityList() {
            const cityList = document.getElementById('cityList');
            if (!cityList) return;
            
            if (!filteredCities || filteredCities.length === 0) {
                cityList.innerHTML = '<div class="no-cities">未找到匹配的城市</div>';
                return;
            }
            
            if (currentViewMode === 'grid') {
                renderGridView(cityList);
            } else {
                renderListView(cityList);
            }
        }

        function renderListView(container) {
            container.className = 'city-list list-view';
            
            const html = filteredCities.map(city => `
                <div class="city-item" data-code="${city.code}">
                    <div class="city-main-info">
                        <div class="city-name">
                            <span class="name-en">${city.name}</span>
                            <span class="name-zh">${city.chinese}</span>
                        </div>
                        <div class="city-details">
                            <span class="city-state">📍 ${city.stateName}</span>
                            <span class="city-postcode">📮 ${city.postcode}</span>
                        </div>
                    </div>
                    <div class="city-actions">
                        <button class="use-city-btn" onclick="useCityForForm('${city.code}')">
                            使用
                        </button>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        function renderGridView(container) {
            container.className = 'city-list grid-view';
            
            const html = filteredCities.map(city => `
                <div class="city-card" data-code="${city.code}">
                    <div class="city-card-header">
                        <div class="city-name">
                            <div class="name-en">${city.name}</div>
                            <div class="name-zh">${city.chinese}</div>
                        </div>
                    </div>
                    <div class="city-card-body">
                        <div class="city-info-item">
                            <span class="info-label">州属:</span>
                            <span class="info-value">${city.stateName}</span>
                        </div>
                        <div class="city-info-item">
                            <span class="info-label">邮编:</span>
                            <span class="info-value">${city.postcode}</span>
                        </div>
                    </div>
                    <div class="city-card-footer">
                        <button class="use-city-btn" onclick="useCityForForm('${city.code}')">
                            使用此城市
                        </button>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        function updateCityStats() {
            const statsEl = document.getElementById('cityListStats');
            if (statsEl) {
                const total = filteredCities.length;
                const totalAll = currentCities.length;
                
                if (total === totalAll) {
                    statsEl.textContent = `共 ${total} 个城市`;
                } else {
                    statsEl.textContent = `显示 ${total} 个城市 (共 ${totalAll} 个)`;
                }
            }
        }

        function useCityForForm(cityCode) {
            const cityDetails = validator.getCityDetails(cityCode);
            if (cityDetails) {
                alert(`已选择城市：${cityDetails.name} (${cityDetails.chinese})\n州属：${validator.codeMappings.states[cityDetails.stateCode]}\n邮编：${cityDetails.postcodeRange ? cityDetails.postcodeRange.join('-') : 'N/A'}`);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializeTest);
    </script>
</body>
</html>
