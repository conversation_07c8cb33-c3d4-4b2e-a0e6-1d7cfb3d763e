<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>马来西亚州属城市数据测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .test-section h2 {
            color: #34495e;
            margin-bottom: 15px;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: inline-block;
            width: 150px;
            font-weight: bold;
        }
        .input-group input, .input-group select {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 200px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background-color: #e8f5e8;
            border-left: 4px solid #27ae60;
            border-radius: 4px;
        }
        .error {
            background-color: #fdf2f2;
            border-left-color: #e74c3c;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .city-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            background-color: white;
        }
        .city-item {
            padding: 5px 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }
        .city-item:last-child {
            border-bottom: none;
        }
        .city-code {
            font-weight: bold;
            color: #3498db;
        }
        .city-chinese {
            color: #e74c3c;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇲🇾 马来西亚州属城市数据测试</h1>
        
        <!-- 数据统计 -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="stateCount">16</div>
                <div>个州属</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="cityCount">0</div>
                <div>个城市</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="popularCount">0</div>
                <div>个热门目的地</div>
            </div>
        </div>

        <!-- 测试区域1: 州属匹配 -->
        <div class="test-section">
            <h2>🌍 州属智能匹配测试</h2>
            <div class="input-group">
                <label for="stateInput">输入州属名称:</label>
                <input type="text" id="stateInput" placeholder="例如: 柔佛, Johor, 吉隆坡" />
                <button onclick="testStateMapping()">测试匹配</button>
            </div>
            <div id="stateResult" class="result" style="display:none;"></div>
        </div>

        <!-- 测试区域2: 城市匹配 -->
        <div class="test-section">
            <h2>🏙️ 城市智能匹配测试</h2>
            <div class="input-group">
                <label for="cityInput">输入城市名称:</label>
                <input type="text" id="cityInput" placeholder="例如: 新山, Kuala Lumpur, 乔治市" />
                <button onclick="testCityMapping()">测试匹配</button>
            </div>
            <div class="input-group">
                <label for="postcodeInput">输入邮政编码:</label>
                <input type="text" id="postcodeInput" placeholder="例如: 50000, 81300" />
                <button onclick="testPostcodeMapping()">邮编匹配</button>
            </div>
            <div id="cityResult" class="result" style="display:none;"></div>
        </div>

        <!-- 测试区域3: 州属城市列表 -->
        <div class="test-section">
            <h2>📋 州属城市列表</h2>
            <div class="input-group">
                <label for="stateSelect">选择州属:</label>
                <select id="stateSelect" onchange="showCitiesByState()">
                    <option value="">-- 选择州属 --</option>
                </select>
            </div>
            <div id="citiesDisplay" class="city-list" style="display:none;"></div>
        </div>

        <!-- 测试区域4: 热门目的地 -->
        <div class="test-section">
            <h2>🎯 热门旅游目的地</h2>
            <button onclick="showPopularDestinations()">显示热门目的地</button>
            <div id="popularDisplay" class="city-list" style="display:none;"></div>
        </div>

        <!-- 测试区域5: 完整数据验证 -->
        <div class="test-section">
            <h2>✅ 数据完整性验证</h2>
            <button onclick="validateAllData()">验证所有数据</button>
            <div id="validationResult" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        let malaysiadata = null;
        let validator = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // 加载马来西亚数据
                const response = await fetch('config/malaysia-states-cities.json');
                malaysiadata = await response.json();
                
                // 初始化验证器（模拟）
                validator = {
                    smartStateMapping: function(value) {
                        if (!value) return null;
                        
                        const cleanValue = value.toString().trim();
                        const states = malaysiadata.states;
                        
                        // 直接代码匹配
                        if (states[cleanValue]) return cleanValue;
                        
                        // 州属名称映射
                        const stateNameMapping = {
                            'johor': '01', '柔佛': '01',
                            'kedah': '02', '吉打': '02',
                            'kelantan': '03', '吉兰丹': '03',
                            'melaka': '04', 'malacca': '04', '马六甲': '04',
                            'negeri sembilan': '05', '森美兰': '05',
                            'pahang': '06', '彭亨': '06',
                            'pulau pinang': '07', 'penang': '07', '槟城': '07',
                            'perak': '08', '霹雳': '08',
                            'perlis': '09', '玻璃市': '09',
                            'selangor': '10', '雪兰莪': '10',
                            'terengganu': '11', '登嘉楼': '11',
                            'sabah': '12', '沙巴': '12',
                            'sarawak': '13', '砂拉越': '13',
                            'kuala lumpur': '14', 'kl': '14', '吉隆坡': '14',
                            'labuan': '15', '纳闽': '15',
                            'putrajaya': '16', '布城': '16'
                        };
                        
                        return stateNameMapping[cleanValue.toLowerCase()] || null;
                    },
                    
                    smartCityMapping: function(value, stateCode = null) {
                        if (!value) return null;
                        
                        const cleanValue = value.toString().trim();
                        
                        // 遍历所有城市
                        for (const [stateKey, stateInfo] of Object.entries(malaysiadata.cities)) {
                            if (stateCode && stateKey !== stateCode) continue;
                            
                            for (const [cityCode, cityInfo] of Object.entries(stateInfo.cities)) {
                                if (cityInfo.name.toLowerCase() === cleanValue.toLowerCase() ||
                                    cityInfo.chinese === cleanValue) {
                                    return cityCode;
                                }
                            }
                        }
                        
                        return null;
                    },
                    
                    getCityByPostcode: function(postcode) {
                        const postcodeNum = parseInt(postcode.toString().replace(/\D/g, ''));
                        if (isNaN(postcodeNum)) return null;
                        
                        for (const [stateKey, stateInfo] of Object.entries(malaysiadata.cities)) {
                            for (const [cityCode, cityInfo] of Object.entries(stateInfo.cities)) {
                                if (cityInfo.postcode) {
                                    const [min, max] = cityInfo.postcode.split('-').map(s => parseInt(s));
                                    if (postcodeNum >= min && postcodeNum <= max) {
                                        return cityCode;
                                    }
                                }
                            }
                        }
                        return null;
                    }
                };
                
                // 更新统计数据
                updateStats();
                
                // 填充州属选择器
                populateStateSelector();
                
                console.log('✅ 马来西亚数据加载完成');
            } catch (error) {
                console.error('❌ 数据加载失败:', error);
            }
        });

        function updateStats() {
            if (!malaysiadata) return;
            
            const stateCount = Object.keys(malaysiadata.states).length;
            let cityCount = 0;
            
            Object.values(malaysiadata.cities).forEach(stateInfo => {
                cityCount += Object.keys(stateInfo.cities).length;
            });
            
            const popularCount = malaysiadata.popularDestinations.tourist_cities.length;
            
            document.getElementById('stateCount').textContent = stateCount;
            document.getElementById('cityCount').textContent = cityCount;
            document.getElementById('popularCount').textContent = popularCount;
        }

        function populateStateSelector() {
            const select = document.getElementById('stateSelect');
            Object.entries(malaysiadata.states).forEach(([code, name]) => {
                const option = document.createElement('option');
                option.value = code;
                option.textContent = `${code} - ${name}`;
                select.appendChild(option);
            });
        }

        function testStateMapping() {
            const input = document.getElementById('stateInput').value;
            const result = validator.smartStateMapping(input);
            const resultDiv = document.getElementById('stateResult');
            
            if (result) {
                const stateName = malaysiadata.states[result];
                resultDiv.innerHTML = `✅ 匹配成功: <strong>${input}</strong> → 代码: <span class="city-code">${result}</span> (${stateName})`;
                resultDiv.className = 'result';
            } else {
                resultDiv.innerHTML = `❌ 匹配失败: <strong>${input}</strong> 未找到对应的州属`;
                resultDiv.className = 'result error';
            }
            resultDiv.style.display = 'block';
        }

        function testCityMapping() {
            const input = document.getElementById('cityInput').value;
            const result = validator.smartCityMapping(input);
            const resultDiv = document.getElementById('cityResult');
            
            if (result) {
                // 找到城市信息
                let cityInfo = null;
                let stateName = '';
                
                for (const [stateKey, stateInfo] of Object.entries(malaysiadata.cities)) {
                    if (stateInfo.cities[result]) {
                        cityInfo = stateInfo.cities[result];
                        stateName = malaysiadata.states[stateKey];
                        break;
                    }
                }
                
                if (cityInfo) {
                    resultDiv.innerHTML = `✅ 匹配成功: <strong>${input}</strong> → 代码: <span class="city-code">${result}</span><br>
                                         城市: ${cityInfo.name} (<span class="city-chinese">${cityInfo.chinese}</span>)<br>
                                         州属: ${stateName}<br>
                                         邮编: ${cityInfo.postcode}`;
                    resultDiv.className = 'result';
                }
            } else {
                resultDiv.innerHTML = `❌ 匹配失败: <strong>${input}</strong> 未找到对应的城市`;
                resultDiv.className = 'result error';
            }
            resultDiv.style.display = 'block';
        }

        function testPostcodeMapping() {
            const input = document.getElementById('postcodeInput').value;
            const result = validator.getCityByPostcode(input);
            const resultDiv = document.getElementById('cityResult');
            
            if (result) {
                // 找到城市信息
                let cityInfo = null;
                let stateName = '';
                
                for (const [stateKey, stateInfo] of Object.entries(malaysiadata.cities)) {
                    if (stateInfo.cities[result]) {
                        cityInfo = stateInfo.cities[result];
                        stateName = malaysiadata.states[stateKey];
                        break;
                    }
                }
                
                if (cityInfo) {
                    resultDiv.innerHTML = `✅ 邮编匹配成功: <strong>${input}</strong> → 代码: <span class="city-code">${result}</span><br>
                                         城市: ${cityInfo.name} (<span class="city-chinese">${cityInfo.chinese}</span>)<br>
                                         州属: ${stateName}`;
                    resultDiv.className = 'result';
                }
            } else {
                resultDiv.innerHTML = `❌ 邮编匹配失败: <strong>${input}</strong> 未找到对应的城市`;
                resultDiv.className = 'result error';
            }
            resultDiv.style.display = 'block';
        }

        function showCitiesByState() {
            const stateCode = document.getElementById('stateSelect').value;
            const displayDiv = document.getElementById('citiesDisplay');
            
            if (!stateCode) {
                displayDiv.style.display = 'none';
                return;
            }
            
            const stateInfo = malaysiadata.cities[stateCode];
            if (!stateInfo) return;
            
            let html = '';
            Object.entries(stateInfo.cities).forEach(([code, cityInfo]) => {
                html += `
                    <div class="city-item">
                        <div>
                            <span class="city-code">${code}</span> - 
                            ${cityInfo.name} 
                            <span class="city-chinese">(${cityInfo.chinese})</span>
                        </div>
                        <div style="font-size: 0.9em; color: #666;">${cityInfo.postcode}</div>
                    </div>
                `;
            });
            
            displayDiv.innerHTML = html;
            displayDiv.style.display = 'block';
        }

        function showPopularDestinations() {
            const displayDiv = document.getElementById('popularDisplay');
            
            let html = '';
            malaysiadata.popularDestinations.tourist_cities.forEach(destination => {
                html += `
                    <div class="city-item">
                        <div>
                            <span class="city-code">${destination.code}</span> - 
                            ${destination.name} 
                            <span class="city-chinese">(${destination.chinese})</span>
                        </div>
                        <div style="font-size: 0.9em; color: #666;">${destination.reason}</div>
                    </div>
                `;
            });
            
            displayDiv.innerHTML = html;
            displayDiv.style.display = 'block';
        }

        function validateAllData() {
            const resultDiv = document.getElementById('validationResult');
            let issues = [];
            let totalCities = 0;
            
            // 验证数据完整性
            Object.entries(malaysiadata.cities).forEach(([stateCode, stateInfo]) => {
                const stateName = malaysiadata.states[stateCode];
                if (!stateName) {
                    issues.push(`州属代码 ${stateCode} 没有对应的州属名称`);
                }
                
                Object.entries(stateInfo.cities).forEach(([cityCode, cityInfo]) => {
                    totalCities++;
                    
                    if (!cityInfo.name) {
                        issues.push(`城市代码 ${cityCode} 缺少英文名称`);
                    }
                    if (!cityInfo.chinese) {
                        issues.push(`城市代码 ${cityCode} 缺少中文名称`);
                    }
                    if (!cityInfo.postcode) {
                        issues.push(`城市代码 ${cityCode} 缺少邮政编码范围`);
                    }
                });
            });
            
            if (issues.length === 0) {
                resultDiv.innerHTML = `
                    ✅ 数据验证通过！<br>
                    • 总共 ${Object.keys(malaysiadata.states).length} 个州属<br>
                    • 总共 ${totalCities} 个城市<br>
                    • 所有数据完整性检查通过
                `;
                resultDiv.className = 'result';
            } else {
                resultDiv.innerHTML = `
                    ❌ 发现 ${issues.length} 个问题：<br>
                    ${issues.map(issue => `• ${issue}`).join('<br>')}
                `;
                resultDiv.className = 'result error';
            }
            
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
