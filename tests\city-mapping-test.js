/**
 * MDAC城市代码匹配测试验证脚本
 * 验证修复后的智能匹配逻辑是否能正确处理"新山乐高"等地址
 */

// 测试数据
const testCases = [
    {
        name: "新山乐高测试",
        address: "新山乐高乐园酒店",
        expectedState: "01",
        expectedCity: "0118"
    },
    {
        name: "新山测试",
        address: "新山市中心",
        expectedState: "01", 
        expectedCity: "0118"
    },
    {
        name: "Johor Bahru测试",
        address: "Johor Bahru Hotel",
        expectedState: "01",
        expectedCity: "0118"
    },
    {
        name: "Legoland测试",
        address: "Legoland Hotel",
        expectedState: "01",
        expectedCity: "0118"
    },
    {
        name: "直接代码测试",
        city: "0118",
        expectedState: "01",
        expectedCity: "0118"
    }
];

// 模拟智能匹配函数（从enhanced-form-filler.js复制）
function smartMatchCityCode(value, stateCode = null) {
    console.log(`🏙️ 智能匹配城市代码: ${value}, 州属: ${stateCode}`);
    
    const cityMapping = {
        // 直接代码匹配
        '0100': '0100',  // Johor
        '0118': '0118',  // <PERSON><PERSON> Bahru
        
        // 地名到代码的映射 (忽略大小写)
        'johor bahru': '0118',
        '新山': '0118',
        '新山乐高': '0118',
        'legoland': '0118',
        '乐高': '0118',
        'johor': '0100',
        '柔佛': '0100'
    };
    
    // 1. 尝试直接匹配
    if (cityMapping[value]) {
        return cityMapping[value];
    }
    
    // 2. 尝试忽略大小写匹配
    const lowerValue = value.toString().toLowerCase();
    if (cityMapping[lowerValue]) {
        return cityMapping[lowerValue];
    }
    
    // 3. 关键词匹配
    if (lowerValue.includes('johor bahru') || lowerValue.includes('新山') || 
        lowerValue.includes('legoland') || lowerValue.includes('乐高')) {
        return '0118';
    }
    
    return value;
}

// 运行测试
function runTests() {
    console.log('🧪 开始MDAC城市代码匹配测试...\n');
    
    let passedTests = 0;
    let totalTests = testCases.length;
    
    testCases.forEach((testCase, index) => {
        console.log(`测试 ${index + 1}: ${testCase.name}`);
        
        let testValue = testCase.city || testCase.address;
        let result = smartMatchCityCode(testValue, testCase.expectedState);
        
        if (result === testCase.expectedCity) {
            console.log(`✅ 通过 - 输入: "${testValue}" → 输出: "${result}"`);
            passedTests++;
        } else {
            console.log(`❌ 失败 - 输入: "${testValue}" → 期望: "${testCase.expectedCity}" → 实际: "${result}"`);
        }
        console.log('');
    });
    
    console.log(`🎯 测试结果: ${passedTests}/${totalTests} 通过`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！城市代码匹配修复成功！');
    } else {
        console.log('⚠️ 部分测试失败，需要进一步修复');
    }
}

// 执行测试
runTests();

// 导出到浏览器控制台使用
if (typeof window !== 'undefined') {
    window.testMDACCityMapping = runTests;
    window.smartMatchCityCode = smartMatchCityCode;
    console.log('💡 提示：在浏览器控制台中运行 testMDACCityMapping() 来测试城市代码匹配');
}
