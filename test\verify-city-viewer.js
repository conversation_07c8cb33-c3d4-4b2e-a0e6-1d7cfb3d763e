// 城市查看器功能验证脚本
// 运行: node test/verify-city-viewer.js

const fs = require('fs');
const path = require('path');

console.log('🏙️ 城市查看器功能验证开始...\n');

// 1. 检查数据文件
console.log('📂 检查数据文件...');
const dataFile = path.join(__dirname, '../config/malaysia-states-cities.json');
if (!fs.existsSync(dataFile)) {
    console.error('❌ 数据文件不存在:', dataFile);
    process.exit(1);
}

const data = JSON.parse(fs.readFileSync(dataFile, 'utf8'));
console.log('✅ 数据文件加载成功');

// 2. 验证数据结构
console.log('\n📊 验证数据结构...');
const requiredKeys = ['states', 'cities', 'cityMapping', 'popularDestinations'];
for (const key of requiredKeys) {
    if (!data[key]) {
        console.error(`❌ 缺少必需的数据字段: ${key}`);
        process.exit(1);
    }
    console.log(`✅ 字段 ${key} 存在`);
}

// 3. 统计数据
console.log('\n📈 数据统计:');
const statesCount = Object.keys(data.states).length;
const citiesCount = Object.values(data.cities).reduce((total, state) => 
    total + Object.keys(state.cities).length, 0);
const popularCount = data.popularDestinations.tourist_cities.length;

console.log(`   州属总数: ${statesCount}`);
console.log(`   城市总数: ${citiesCount}`);
console.log(`   热门目的地: ${popularCount}`);

// 4. 验证数据完整性
console.log('\n🔍 验证数据完整性...');
let errors = 0;

// 检查州属
Object.entries(data.states).forEach(([code, name]) => {
    if (!code || !name) {
        console.error(`❌ 州属数据不完整: ${code} - ${name}`);
        errors++;
    }
});

// 检查城市
Object.entries(data.cities).forEach(([stateCode, stateInfo]) => {
    if (!stateInfo.stateName || !stateInfo.cities) {
        console.error(`❌ 州属城市数据不完整: ${stateCode}`);
        errors++;
        return;
    }
    
    Object.entries(stateInfo.cities).forEach(([cityCode, cityInfo]) => {
        if (!cityInfo.name || !cityInfo.chinese || !cityInfo.postcode) {
            console.error(`❌ 城市数据不完整: ${cityCode} - ${cityInfo.name}`);
            errors++;
        }
    });
});

if (errors === 0) {
    console.log('✅ 数据完整性验证通过');
} else {
    console.error(`❌ 发现 ${errors} 个数据错误`);
}

// 5. 测试搜索功能（模拟）
console.log('\n🔍 模拟搜索功能测试...');

// 创建简化的搜索函数
function searchCities(query, limit = 10) {
    const results = [];
    const searchQuery = query.toLowerCase();
    
    Object.entries(data.cities).forEach(([stateCode, stateInfo]) => {
        Object.entries(stateInfo.cities).forEach(([cityCode, cityInfo]) => {
            const searchText = `${cityInfo.name} ${cityInfo.chinese} ${stateInfo.stateName}`.toLowerCase();
            if (searchText.includes(searchQuery)) {
                results.push({
                    code: cityCode,
                    name: cityInfo.name,
                    chinese: cityInfo.chinese,
                    state: stateInfo.stateName,
                    postcode: cityInfo.postcode
                });
            }
        });
    });
    
    return results.slice(0, limit);
}

// 测试搜索
const testQueries = ['吉隆坡', 'Johor', 'George', '新山', 'Penang'];
console.log('测试搜索查询:');
testQueries.forEach(query => {
    const results = searchCities(query, 3);
    console.log(`   "${query}": 找到 ${results.length} 个结果`);
    if (results.length > 0) {
        console.log(`     - ${results[0].name} (${results[0].chinese})`);
    }
});

// 6. 检查JavaScript文件
console.log('\n📄 检查JavaScript文件...');
const jsFiles = [
    '../ui/ui-sidepanel.js',
    '../utils/mdac-validator.js'
];

jsFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
        console.log(`✅ ${file} 存在`);
        
        // 检查是否包含城市查看器相关代码
        const content = fs.readFileSync(filePath, 'utf8');
        if (content.includes('cityViewer') || content.includes('城市查看器')) {
            console.log(`   ✅ 包含城市查看器相关代码`);
        }
    } else {
        console.error(`❌ ${file} 不存在`);
        errors++;
    }
});

// 7. 检查CSS文件
console.log('\n🎨 检查CSS文件...');
const cssFile = path.join(__dirname, '../ui/ui-sidepanel.css');
if (fs.existsSync(cssFile)) {
    console.log('✅ CSS文件存在');
    const cssContent = fs.readFileSync(cssFile, 'utf8');
    if (cssContent.includes('city-viewer') || cssContent.includes('city-card')) {
        console.log('   ✅ 包含城市查看器样式');
    } else {
        console.error('   ❌ 缺少城市查看器样式');
        errors++;
    }
} else {
    console.error('❌ CSS文件不存在');
    errors++;
}

// 8. 总结
console.log('\n📋 验证总结:');
if (errors === 0) {
    console.log('🎉 所有验证项目都通过了！');
    console.log('✅ 城市查看器功能已准备就绪');
    console.log('\n📌 使用说明:');
    console.log('1. 在Chrome扩展侧边栏中点击 🏙️ "城市查看器" 按钮');
    console.log('2. 使用搜索框搜索城市（支持中英文）');
    console.log('3. 使用州属过滤器筛选特定州属的城市');
    console.log('4. 点击"热门目的地"查看旅游城市');
    console.log('5. 切换列表/网格视图获得不同的显示效果');
    console.log('6. 点击"使用"按钮将城市信息填充到表单中');
} else {
    console.error(`❌ 发现 ${errors} 个问题，请修复后重新验证`);
    process.exit(1);
}

console.log('\n🏙️ 城市查看器功能验证完成');
