/**
 * MDAC AI智能填充工具 - 后台服务脚本 (传统版本)
 * 使用传统语法确保最大兼容性
 */

// 内置配置，避免模块依赖
var DEFAULT_CONFIG = {
    GEMINI_API_KEY: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',
    GEMINI_MODEL: 'gemini-2.5-flash-lite-preview-06-17',
    API_BASE_URL: 'https://generativelanguage.googleapis.com/v1beta/models'
};

// 后台服务类
function MDACBackground() {
    this.aiCache = new Map();
    this.config = DEFAULT_CONFIG;
    this.init();
}

MDACBackground.prototype.init = function() {
    this.setupEventListeners();
    this.initializeStorage();
    console.log('MDAC AI扩展后台服务已启动 (传统版本)');
};

MDACBackground.prototype.setupEventListeners = function() {
    var self = this;
    
    // 扩展安装/更新事件
    chrome.runtime.onInstalled.addListener(function(details) {
        self.handleInstalled(details);
    });

    // 消息传递
    chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
        self.handleMessage(message, sender, sendResponse);
        return true; // 保持消息通道开放
    });

    // 快捷键命令
    chrome.commands.onCommand.addListener(function(command) {
        self.handleCommand(command);
    });
};

MDACBackground.prototype.initializeStorage = function() {
    var self = this;
    
    chrome.storage.sync.get(['mdacSettings'], function(storage) {
        if (chrome.runtime.lastError) {
            console.error('读取存储失败:', chrome.runtime.lastError);
            return;
        }
        
        if (!storage.mdacSettings) {
            chrome.storage.sync.set({
                mdacSettings: {
                    aiEnabled: true,
                    geminiApiKey: self.config.GEMINI_API_KEY,
                    aiModel: self.config.GEMINI_MODEL,
                    aiTemperature: 0.2,
                    debugMode: false
                }
            }, function() {
                if (chrome.runtime.lastError) {
                    console.error('初始化设置失败:', chrome.runtime.lastError);
                } else {
                    console.log('已初始化AI默认设置');
                }
            });
        }
    });
};

MDACBackground.prototype.handleInstalled = function(details) {
    if (details.reason === 'install') {
        console.log('MDAC AI扩展首次安装');
        chrome.tabs.create({
            url: chrome.runtime.getURL('src/ui/options/options.html')
        }, function() {
            if (chrome.runtime.lastError) {
                console.error('打开设置页面失败:', chrome.runtime.lastError);
            }
        });
    } else if (details.reason === 'update') {
        console.log('MDAC AI扩展已更新到版本:', chrome.runtime.getManifest().version);
    }
};

MDACBackground.prototype.handleMessage = function(message, sender, sendResponse) {
    var self = this;
    
    try {
        switch (message.action) {
            case 'test-ai-connection':
                self.testAIConnection(function(result) {
                    sendResponse({ success: true, result: result });
                });
                break;

            case 'get-ai-response':
                self.getAIResponse(message.prompt, message.context, function(result) {
                    sendResponse({ success: true, result: result });
                }, function(error) {
                    sendResponse({ success: false, error: error.message });
                });
                break;

            case 'analyze-content':
                self.analyzeContent(message.content, message.type, function(result) {
                    sendResponse({ success: true, result: result });
                }, function(error) {
                    sendResponse({ success: false, error: error.message });
                });
                break;

            case 'callGeminiAI':
                self.callGeminiAI(message.prompt, message.context, function(result) {
                    sendResponse({ success: true, data: result });
                }, function(error) {
                    sendResponse({ success: false, error: error.message });
                });
                break;

            case 'callGeminiVision':
                self.callGeminiVision(message.image, message.prompt, function(result) {
                    sendResponse({ success: true, data: result });
                }, function(error) {
                    sendResponse({ success: false, error: error.message });
                });
                break;

            case 'open-side-panel':
                chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
                    if (tabs[0]) {
                        chrome.sidePanel.open({ tabId: tabs[0].id }, function() {
                            if (chrome.runtime.lastError) {
                                sendResponse({ success: false, error: chrome.runtime.lastError.message });
                            } else {
                                sendResponse({ success: true });
                            }
                        });
                    } else {
                        sendResponse({ success: false, error: '无法获取当前标签页' });
                    }
                });
                break;

            case 'check-side-panel-support':
                sendResponse({
                    success: true,
                    supported: typeof chrome.sidePanel !== 'undefined'
                });
                break;

            default:
                sendResponse({ success: false, error: '未知操作' });
        }
    } catch (error) {
        console.error('处理消息失败:', error);
        sendResponse({ success: false, error: error.message });
    }
};

MDACBackground.prototype.handleCommand = function(command) {
    if (command === 'fill-form') {
        this.triggerFormFill();
    }
};

MDACBackground.prototype.triggerFormFill = function() {
    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
        if (chrome.runtime.lastError) {
            console.error('查询标签页失败:', chrome.runtime.lastError);
            return;
        }
        
        var tab = tabs[0];
        if (tab && tab.url.includes('imigresen-online.imi.gov.my')) {
            chrome.tabs.sendMessage(tab.id, { action: 'start-ai-fill' }, function() {
                if (chrome.runtime.lastError) {
                    console.error('发送消息失败:', chrome.runtime.lastError);
                }
            });
        }
    });
};

MDACBackground.prototype.testAIConnection = function(callback) {
    var self = this;
    
    chrome.storage.sync.get(['mdacSettings'], function(settings) {
        if (chrome.runtime.lastError) {
            callback({ connected: false, error: chrome.runtime.lastError.message });
            return;
        }
        
        var apiKey = (settings.mdacSettings && settings.mdacSettings.geminiApiKey) || self.config.GEMINI_API_KEY;
        var url = self.config.API_BASE_URL + '/' + self.config.GEMINI_MODEL + ':generateContent?key=' + apiKey;
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{ text: '请回复"连接正常"' }]
                }],
                generationConfig: {
                    temperature: 0,
                    maxOutputTokens: 50
                }
            })
        })
        .then(function(response) {
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('API请求失败: ' + response.status);
            }
        })
        .then(function(data) {
            var responseText = data.candidates && data.candidates[0] && 
                              data.candidates[0].content && data.candidates[0].content.parts &&
                              data.candidates[0].content.parts[0] && data.candidates[0].content.parts[0].text;
            
            callback({
                connected: true,
                response: responseText || '连接成功'
            });
        })
        .catch(function(error) {
            console.error('AI连接测试失败:', error);
            callback({ connected: false, error: error.message });
        });
    });
};

MDACBackground.prototype.getAIResponse = function(prompt, context, successCallback, errorCallback) {
    var self = this;
    
    chrome.storage.sync.get(['mdacSettings'], function(settings) {
        if (chrome.runtime.lastError) {
            errorCallback(new Error(chrome.runtime.lastError.message));
            return;
        }
        
        var apiKey = (settings.mdacSettings && settings.mdacSettings.geminiApiKey) || self.config.GEMINI_API_KEY;
        var fullPrompt = context ? context + '\n\n' + prompt : prompt;
        var url = self.config.API_BASE_URL + '/' + self.config.GEMINI_MODEL + ':generateContent?key=' + apiKey;
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{ text: fullPrompt }]
                }],
                generationConfig: {
                    temperature: 0.2,
                    maxOutputTokens: 1024
                }
            })
        })
        .then(function(response) {
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('API请求失败: ' + response.status);
            }
        })
        .then(function(data) {
            var responseText = data.candidates && data.candidates[0] && 
                              data.candidates[0].content && data.candidates[0].content.parts &&
                              data.candidates[0].content.parts[0] && data.candidates[0].content.parts[0].text;
            
            successCallback(responseText || '');
        })
        .catch(function(error) {
            console.error('获取AI响应失败:', error);
            errorCallback(error);
        });
    });
};

MDACBackground.prototype.analyzeContent = function(content, type, successCallback, errorCallback) {
    type = type || 'general';
    
    var prompts = {
        general: '请分析以下内容并提取相关信息：\n\n' + content,
        form: '请从以下内容中提取适合MDAC表单的信息：\n\n' + content,
        address: '请将以下地址翻译为英文：\n\n' + content
    };

    var prompt = prompts[type] || prompts.general;
    this.getAIResponse(prompt, '', successCallback, errorCallback);
};

/**
 * 调用Gemini AI API
 */
MDACBackground.prototype.callGeminiAI = function(prompt, context, successCallback, errorCallback) {
    var self = this;

    chrome.storage.sync.get(['mdacSettings'], function(settings) {
        if (chrome.runtime.lastError) {
            errorCallback(new Error(chrome.runtime.lastError.message));
            return;
        }

        var apiKey = (settings.mdacSettings && settings.mdacSettings.geminiApiKey) || self.config.GEMINI_API_KEY;
        if (!apiKey) {
            errorCallback(new Error('请先在设置中配置Gemini API密钥'));
            return;
        }

        var requestBody = {
            contents: [{
                parts: [{
                    text: context ? context + '\n\n' + prompt : prompt
                }]
            }],
            generationConfig: {
                temperature: 0.1,
                topK: 32,
                topP: 1,
                maxOutputTokens: 4096
            }
        };

        var url = self.config.API_BASE_URL + '/' + self.config.GEMINI_MODEL + ':generateContent?key=' + apiKey;

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        }).then(function(response) {
            if (!response.ok) {
                throw new Error('Gemini API错误: ' + response.statusText);
            }
            return response.json();
        }).then(function(data) {
            if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                throw new Error('Gemini API返回格式异常');
            }

            var result = data.candidates[0].content.parts[0].text;
            console.log('Gemini AI API响应成功');
            successCallback(result);
        }).catch(function(error) {
            console.error('Gemini AI API调用失败:', error);
            errorCallback(error);
        });
    });
};

/**
 * 调用Gemini Vision API处理图片
 */
MDACBackground.prototype.callGeminiVision = function(base64Image, prompt, successCallback, errorCallback) {
    var self = this;

    chrome.storage.sync.get(['mdacSettings'], function(settings) {
        if (chrome.runtime.lastError) {
            errorCallback(new Error(chrome.runtime.lastError.message));
            return;
        }

        var apiKey = (settings.mdacSettings && settings.mdacSettings.geminiApiKey) || self.config.GEMINI_API_KEY;
        if (!apiKey) {
            errorCallback(new Error('请先在设置中配置Gemini API密钥'));
            return;
        }

        var requestBody = {
            contents: [{
                parts: [
                    {
                        text: prompt
                    },
                    {
                        inline_data: {
                            mime_type: "image/jpeg",
                            data: base64Image
                        }
                    }
                ]
            }],
            generationConfig: {
                temperature: 0.1,
                topK: 32,
                topP: 1,
                maxOutputTokens: 4096
            }
        };

        var url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=' + apiKey;

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        }).then(function(response) {
            if (!response.ok) {
                throw new Error('Gemini Vision API错误: ' + response.statusText);
            }
            return response.json();
        }).then(function(data) {
            if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                throw new Error('Gemini Vision API返回格式异常');
            }

            var result = data.candidates[0].content.parts[0].text;
            console.log('Gemini Vision API响应成功');
            successCallback(result);
        }).catch(function(error) {
            console.error('Gemini Vision API调用失败:', error);
            errorCallback(error);
        });
    });
};

// 添加扩展图标点击处理 - 打开侧边栏
chrome.action.onClicked.addListener(function(tab) {
    console.log('扩展图标被点击，打开侧边栏');
    // 打开侧边栏
    chrome.sidePanel.open({ tabId: tab.id }).catch(function(error) {
        console.error('打开侧边栏失败:', error);
    });
});

// 监听快捷键命令
chrome.commands.onCommand.addListener(function(command) {
    if (command === 'open-side-panel') {
        chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
            if (tabs[0]) {
                chrome.sidePanel.open({ tabId: tabs[0].id }).catch(function(error) {
                    console.error('快捷键打开侧边栏失败:', error);
                });
            }
        });
    }
});

// 初始化后台服务
var mdacBackground = new MDACBackground();
