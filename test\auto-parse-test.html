<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动解析功能测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        .test-data {
            background: #ffffff;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #2563eb;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .instructions {
            background: #fef3c7;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #f59e0b;
            margin: 10px 0;
        }
        h1 {
            color: #1e293b;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 10px;
        }
        h2 {
            color: #374151;
            margin-top: 30px;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .success { background: #d1fae5; border-left: 4px solid #10b981; }
        .warning { background: #fef3c7; border-left: 4px solid #f59e0b; }
        .error { background: #fee2e2; border-left: 4px solid #ef4444; }
    </style>
</head>
<body>
    <h1>🧪 MDAC AI自动解析功能测试</h1>
    
    <div class="instructions">
        <h3>📋 测试说明</h3>
        <p>本页面提供测试数据，用于验证MDAC AI Chrome扩展的自动解析功能。请按照以下步骤进行测试：</p>
        <ol>
            <li>确保MDAC AI Chrome扩展已安装并启用</li>
            <li>打开扩展的侧边栏界面</li>
            <li>复制下方的测试数据到相应的输入框</li>
            <li>观察自动解析功能是否正常工作</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>👤 个人信息测试数据</h2>
        
        <h3>测试案例 1: 护照信息</h3>
        <div class="test-data">姓名: ZHANG WEI
护照号码: A12345678
出生日期: 15/03/1990
国籍: 中国
性别: 男</div>
        
        <h3>测试案例 2: 身份证信息</h3>
        <div class="test-data">姓名: 李明
身份证号: 110101199001011234
出生日期: 1990年1月1日
性别: 男
国籍: 中华人民共和国</div>
        
        <h3>测试案例 3: 英文护照信息</h3>
        <div class="test-data">Name: JOHN SMITH
Passport No: B98765432
Date of Birth: 25/12/1985
Nationality: UNITED STATES
Sex: MALE</div>
    </div>

    <div class="test-section">
        <h2>✈️ 旅行信息测试数据</h2>
        
        <h3>测试案例 1: 机票信息</h3>
        <div class="test-data">航班: MH123
出发: 北京首都国际机场 (PEK)
到达: 吉隆坡国际机场 (KUL)
日期: 2024年3月15日
返程: 2024年3月25日
航班: MH124</div>
        
        <h3>测试案例 2: 酒店预订信息</h3>
        <div class="test-data">酒店: Hilton Kuala Lumpur
地址: 3 Jalan Stesen Sentral, Kuala Lumpur City Centre, 50470 Kuala Lumpur
入住: 2024年3月15日
退房: 2024年3月25日
预订号: HLT123456789</div>
        
        <h3>测试案例 3: 完整旅行计划</h3>
        <div class="test-data">出发日期: 2024年3月15日
返回日期: 2024年3月25日
航班号: CZ351 (去程), CZ352 (返程)
住宿: Grand Hyatt Kuala Lumpur
地址: 12 Jalan Pinang, Kuala Lumpur City Centre, 50450 Kuala Lumpur, Malaysia
城市: 吉隆坡</div>
    </div>

    <div class="test-section">
        <h2>🔧 功能测试检查清单</h2>
        
        <div class="status warning">
            <h3>⏱️ 自动解析时间测试</h3>
            <ul>
                <li>输入内容后是否显示3秒倒计时</li>
                <li>倒计时是否准确递减</li>
                <li>倒计时结束后是否自动开始解析</li>
            </ul>
        </div>
        
        <div class="status warning">
            <h3>🎛️ 控制功能测试</h3>
            <ul>
                <li>自动解析开关是否正常工作</li>
                <li>取消按钮是否能中断自动解析</li>
                <li>继续输入是否会重置倒计时</li>
            </ul>
        </div>
        
        <div class="status warning">
            <h3>🎨 界面显示测试</h3>
            <ul>
                <li>倒计时指示器是否正确显示</li>
                <li>解析状态是否有视觉反馈</li>
                <li>动画效果是否流畅</li>
            </ul>
        </div>
        
        <div class="status warning">
            <h3>📊 解析结果测试</h3>
            <ul>
                <li>个人信息是否正确解析</li>
                <li>旅行信息是否正确解析</li>
                <li>解析完成后是否自动显示预览</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🐛 常见问题排查</h2>
        
        <div class="status error">
            <h3>❌ 如果自动解析不工作</h3>
            <ol>
                <li>检查自动解析开关是否开启</li>
                <li>确认输入内容不为空</li>
                <li>查看浏览器控制台是否有错误</li>
                <li>尝试刷新侧边栏页面</li>
            </ol>
        </div>
        
        <div class="status success">
            <h3>✅ 预期正常行为</h3>
            <ul>
                <li>输入停止后3秒自动开始解析</li>
                <li>解析过程中显示加载状态</li>
                <li>解析完成后自动显示结果</li>
                <li>可以随时取消自动解析</li>
            </ul>
        </div>
    </div>

    <div class="instructions">
        <h3>📝 测试报告</h3>
        <p>完成测试后，请记录以下信息：</p>
        <ul>
            <li>自动解析功能是否按预期工作</li>
            <li>倒计时显示是否正确</li>
            <li>解析结果的准确性</li>
            <li>用户界面的响应性</li>
            <li>发现的任何问题或改进建议</li>
        </ul>
    </div>
</body>
</html>
