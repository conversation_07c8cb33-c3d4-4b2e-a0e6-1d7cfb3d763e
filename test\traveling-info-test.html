<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Traveling Information 功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }

        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        h1 {
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: #f8fafc;
        }

        .test-section h2 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .test-case {
            margin-bottom: 20px;
            padding: 15px;
            border-left: 4px solid #4299e1;
            background: white;
            border-radius: 5px;
        }

        .test-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #cbd5e0;
            border-radius: 5px;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .test-button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .test-button:hover {
            background: #3182ce;
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background: #f0fff4;
            border: 1px solid #9ae6b4;
        }

        .result.error {
            background: #fed7d7;
            border-color: #fc8181;
        }

        .result.warning {
            background: #fefcbf;
            border-color: #f6e05e;
        }

        .field-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .field-item {
            padding: 10px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 5px;
        }

        .field-label {
            font-weight: bold;
            color: #4a5568;
            margin-bottom: 5px;
        }

        .field-value {
            color: #2d3748;
            font-family: monospace;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #edf2f7;
            border-radius: 10px;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4299e1;
        }

        .stat-label {
            color: #718096;
            margin-top: 5px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success { color: #38a169; }
        .error { color: #e53e3e; }
        .warning { color: #d69e2e; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Traveling Information 功能测试</h1>
        
        <!-- 统计信息 -->
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalTests">0</div>
                <div class="stat-label">总测试数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="passedTests">0</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="failedTests">0</div>
                <div class="stat-label">失败测试</div>
            </div>
        </div>

        <!-- 测试用例1：新山乐高乐园 -->
        <div class="test-section">
            <h2>🎢 测试用例1：新山乐高乐园</h2>
            <div class="test-case">
                <h3>中文地址解析测试</h3>
                <textarea class="test-input" id="legolandChinese" rows="3" placeholder="输入中文地址...">新山乐高乐园，马来西亚柔佛州依斯干达公主城</textarea>
                <button class="test-button" onclick="testAddressParsing('legolandChinese', 'legolandChineseResult')">🚀 解析中文地址</button>
                <div id="legolandChineseResult"></div>
            </div>
            
            <div class="test-case">
                <h3>英文地址解析测试</h3>
                <textarea class="test-input" id="legolandEnglish" rows="3" placeholder="输入英文地址...">Legoland Malaysia Resort, 7, Jalan Legoland, Bandar Medini Iskandar, 79100 Nusajaya, Johor, Malaysia</textarea>
                <button class="test-button" onclick="testAddressParsing('legolandEnglish', 'legolandEnglishResult')">🚀 解析英文地址</button>
                <div id="legolandEnglishResult"></div>
            </div>
        </div>

        <!-- 测试用例2：吉隆坡双峰塔 -->
        <div class="test-section">
            <h2>🏢 测试用例2：吉隆坡双峰塔</h2>
            <div class="test-case">
                <h3>KLCC地址解析测试</h3>
                <textarea class="test-input" id="klccAddress" rows="3" placeholder="输入地址...">吉隆坡双峰塔，马来西亚吉隆坡市中心</textarea>
                <button class="test-button" onclick="testAddressParsing('klccAddress', 'klccResult')">🚀 解析KLCC地址</button>
                <div id="klccResult"></div>
            </div>
        </div>

        <!-- 测试用例3：槟城乔治市 -->
        <div class="test-section">
            <h2>🏛️ 测试用例3：槟城乔治市</h2>
            <div class="test-case">
                <h3>世界文化遗产地址解析测试</h3>
                <textarea class="test-input" id="penangAddress" rows="3" placeholder="输入地址...">槟城乔治市，马来西亚槟城州</textarea>
                <button class="test-button" onclick="testAddressParsing('penangAddress', 'penangResult')">🚀 解析槟城地址</button>
                <div id="penangResult"></div>
            </div>
        </div>

        <!-- 邮编验证测试 -->
        <div class="test-section">
            <h2>📮 邮编验证测试</h2>
            <div class="test-case">
                <h3>邮编到城市映射测试</h3>
                <input type="text" class="test-input" id="postcodeInput" placeholder="输入5位邮编..." maxlength="5">
                <button class="test-button" onclick="testPostcodeMapping()">🔍 验证邮编</button>
                <div id="postcodeResult"></div>
            </div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <h2>🔄 综合功能测试</h2>
            <div class="test-case">
                <button class="test-button" onclick="runAllTests()">🚀 运行所有测试</button>
                <button class="test-button" onclick="clearAllResults()">🧹 清除结果</button>
                <div id="comprehensiveResult"></div>
            </div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="../config/malaysia-states-cities.json" type="application/json" id="malaysiaData"></script>
    <script src="../utils/mdac-validator.js"></script>
    <script src="../config/ai-config.js"></script>
    <script src="../modules/google-maps-integration.js"></script>
    
    <script>
        // 测试统计
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0
        };

        // 初始化
        let validator;
        let googleMaps;
        let malaysiadata;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('🚀 初始化测试环境...');
            
            try {
                // 加载马来西亚数据
                const response = await fetch('../config/malaysia-states-cities.json');
                malaysiadata = await response.json();
                console.log('✅ 马来西亚数据加载成功');
                
                // 初始化验证器
                validator = new MDACValidator();
                await validator.loadCodeMappings();
                console.log('✅ 验证器初始化成功');
                
                // 初始化Google Maps集成
                if (typeof GoogleMapsIntegration !== 'undefined') {
                    const apiKey = GEMINI_CONFIG?.DEFAULT_API_KEY;
                    if (apiKey) {
                        googleMaps = new GoogleMapsIntegration(apiKey);
                        console.log('✅ Google Maps集成初始化成功');
                    }
                }
                
                console.log('✅ 测试环境初始化完成');
            } catch (error) {
                console.error('❌ 测试环境初始化失败:', error);
            }
        });

        // 更新统计信息
        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;
        }

        // 测试地址解析
        async function testAddressParsing(inputId, resultId) {
            const input = document.getElementById(inputId);
            const resultDiv = document.getElementById(resultId);
            const address = input.value.trim();
            
            if (!address) {
                showResult(resultDiv, '请输入地址', 'warning');
                return;
            }
            
            testStats.total++;
            showResult(resultDiv, '<div class="loading"></div> 正在解析地址...', 'info');
            
            try {
                // 模拟AI解析（这里应该调用实际的AI API）
                const parsedData = await simulateAIParsing(address);
                
                if (parsedData) {
                    showResult(resultDiv, '✅ 地址解析成功', 'success');
                    displayParsedData(resultDiv, parsedData);
                    testStats.passed++;
                } else {
                    showResult(resultDiv, '❌ 地址解析失败', 'error');
                    testStats.failed++;
                }
            } catch (error) {
                showResult(resultDiv, `❌ 解析出错: ${error.message}`, 'error');
                testStats.failed++;
            }
            
            updateStats();
        }

        // 模拟AI解析（实际应用中会调用Gemini API）
        async function simulateAIParsing(address) {
            // 这里模拟AI解析逻辑
            const lowerAddress = address.toLowerCase();
            
            if (lowerAddress.includes('乐高') || lowerAddress.includes('legoland')) {
                return {
                    address: 'Legoland Malaysia Resort, Bandar Medini Iskandar',
                    state: '01',
                    city: '0109',
                    postcode: '79100',
                    stateName: 'Johor',
                    cityName: 'Iskandar Puteri'
                };
            } else if (lowerAddress.includes('双峰塔') || lowerAddress.includes('klcc')) {
                return {
                    address: 'Petronas Twin Towers, KLCC',
                    state: '14',
                    city: '1401',
                    postcode: '50088',
                    stateName: 'Kuala Lumpur',
                    cityName: 'KLCC'
                };
            } else if (lowerAddress.includes('槟城') || lowerAddress.includes('penang') || lowerAddress.includes('乔治市')) {
                return {
                    address: 'George Town, Penang',
                    state: '07',
                    city: '0700',
                    postcode: '10000',
                    stateName: 'Pulau Pinang',
                    cityName: 'George Town'
                };
            }
            
            return null;
        }

        // 显示解析结果
        function displayParsedData(container, data) {
            const fieldsHtml = `
                <div class="field-display">
                    <div class="field-item">
                        <div class="field-label">地址</div>
                        <div class="field-value">${data.address || 'N/A'}</div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">州属</div>
                        <div class="field-value">${data.state || 'N/A'} (${data.stateName || 'N/A'})</div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">城市</div>
                        <div class="field-value">${data.city || 'N/A'} (${data.cityName || 'N/A'})</div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">邮编</div>
                        <div class="field-value">${data.postcode || 'N/A'}</div>
                    </div>
                </div>
            `;
            
            container.innerHTML += fieldsHtml;
        }

        // 测试邮编映射
        function testPostcodeMapping() {
            const input = document.getElementById('postcodeInput');
            const resultDiv = document.getElementById('postcodeResult');
            const postcode = input.value.trim();
            
            if (!postcode || !/^\d{5}$/.test(postcode)) {
                showResult(resultDiv, '请输入有效的5位邮编', 'warning');
                return;
            }
            
            testStats.total++;
            
            try {
                if (validator) {
                    const cityCode = validator.getCityByPostcode(postcode);
                    if (cityCode) {
                        const cityDetails = validator.getCityDetails(cityCode);
                        showResult(resultDiv, `✅ 邮编验证成功: ${postcode} → ${cityDetails.name} (${cityDetails.chinese})`, 'success');
                        testStats.passed++;
                    } else {
                        showResult(resultDiv, `❌ 邮编验证失败: ${postcode} 未找到对应城市`, 'error');
                        testStats.failed++;
                    }
                } else {
                    showResult(resultDiv, '❌ 验证器未初始化', 'error');
                    testStats.failed++;
                }
            } catch (error) {
                showResult(resultDiv, `❌ 验证出错: ${error.message}`, 'error');
                testStats.failed++;
            }
            
            updateStats();
        }

        // 显示结果
        function showResult(container, message, type = 'info') {
            container.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        // 运行所有测试
        async function runAllTests() {
            console.log('🚀 开始运行所有测试...');
            
            // 重置统计
            testStats = { total: 0, passed: 0, failed: 0 };
            
            // 设置测试数据
            document.getElementById('legolandChinese').value = '新山乐高乐园，马来西亚柔佛州依斯干达公主城';
            document.getElementById('legolandEnglish').value = 'Legoland Malaysia Resort, 7, Jalan Legoland, Bandar Medini Iskandar, 79100 Nusajaya, Johor, Malaysia';
            document.getElementById('klccAddress').value = '吉隆坡双峰塔，马来西亚吉隆坡市中心';
            document.getElementById('penangAddress').value = '槟城乔治市，马来西亚槟城州';
            document.getElementById('postcodeInput').value = '79100';
            
            // 运行测试
            await testAddressParsing('legolandChinese', 'legolandChineseResult');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAddressParsing('legolandEnglish', 'legolandEnglishResult');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAddressParsing('klccAddress', 'klccResult');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAddressParsing('penangAddress', 'penangResult');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testPostcodeMapping();
            
            // 显示综合结果
            const comprehensiveDiv = document.getElementById('comprehensiveResult');
            const successRate = testStats.total > 0 ? (testStats.passed / testStats.total * 100).toFixed(1) : 0;
            showResult(comprehensiveDiv, `🎯 测试完成！成功率: ${successRate}% (${testStats.passed}/${testStats.total})`, 
                      successRate >= 80 ? 'success' : successRate >= 60 ? 'warning' : 'error');
            
            console.log('✅ 所有测试运行完成');
        }

        // 清除所有结果
        function clearAllResults() {
            const resultDivs = ['legolandChineseResult', 'legolandEnglishResult', 'klccResult', 'penangResult', 'postcodeResult', 'comprehensiveResult'];
            resultDivs.forEach(id => {
                const div = document.getElementById(id);
                if (div) div.innerHTML = '';
            });
            
            testStats = { total: 0, passed: 0, failed: 0 };
            updateStats();
            
            console.log('🧹 所有结果已清除');
        }
    </script>
</body>
</html>
