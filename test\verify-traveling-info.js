/**
 * Traveling Information 功能验证脚本
 * 运行: node test/verify-traveling-info.js
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Traveling Information 功能验证开始...\n');

// 验证结果统计
let verificationResults = {
    total: 0,
    passed: 0,
    failed: 0,
    warnings: 0
};

/**
 * 验证HTML文件结构
 */
function verifyHTMLStructure() {
    console.log('📄 验证HTML文件结构...');
    
    const htmlFile = path.join(__dirname, '../ui/ui-sidepanel.html');
    if (!fs.existsSync(htmlFile)) {
        logResult('❌ HTML文件不存在', false);
        return;
    }
    
    const htmlContent = fs.readFileSync(htmlFile, 'utf8');
    
    // 检查必需的字段
    const requiredFields = [
        { id: 'state', label: '州属字段' },
        { id: 'city', label: '城市字段' },
        { id: 'postcode', label: '邮编字段' }
    ];
    
    requiredFields.forEach(field => {
        verificationResults.total++;
        if (htmlContent.includes(`id="${field.id}"`)) {
            logResult(`✅ ${field.label}存在`, true);
        } else {
            logResult(`❌ ${field.label}缺失`, false);
        }
    });
    
    // 检查字段类型
    verificationResults.total++;
    if (htmlContent.includes('id="state"') && htmlContent.includes('<select')) {
        logResult('✅ 州属字段为下拉框类型', true);
    } else {
        logResult('❌ 州属字段类型不正确', false);
    }
    
    verificationResults.total++;
    if (htmlContent.includes('id="postcode"') && htmlContent.includes('type="text"')) {
        logResult('✅ 邮编字段为文本输入类型', true);
    } else {
        logResult('❌ 邮编字段类型不正确', false);
    }
}

/**
 * 验证JavaScript文件功能
 */
function verifyJavaScriptFunctions() {
    console.log('\n📜 验证JavaScript文件功能...');
    
    const jsFile = path.join(__dirname, '../ui/ui-sidepanel.js');
    if (!fs.existsSync(jsFile)) {
        logResult('❌ JavaScript文件不存在', false);
        return;
    }
    
    const jsContent = fs.readFileSync(jsFile, 'utf8');
    
    // 检查必需的方法
    const requiredMethods = [
        'initializeTravelInfoFields',
        'initializeStateDropdown',
        'handleStateChange',
        'updateCityDropdown',
        'handleCityChange',
        'autoFillPostcode',
        'handlePostcodeInput',
        'validatePostcode',
        'fillSidePanelFields'
    ];
    
    requiredMethods.forEach(method => {
        verificationResults.total++;
        if (jsContent.includes(`${method}(`)) {
            logResult(`✅ 方法 ${method} 存在`, true);
        } else {
            logResult(`❌ 方法 ${method} 缺失`, false);
        }
    });
    
    // 检查事件监听器
    const eventListeners = [
        'handleStateChange',
        'handleCityChange',
        'handlePostcodeInput'
    ];
    
    eventListeners.forEach(listener => {
        verificationResults.total++;
        if (jsContent.includes(`addEventListener('change'`) || jsContent.includes(`addEventListener('input'`)) {
            logResult(`✅ 事件监听器配置存在`, true);
        } else {
            logResult(`⚠️ 事件监听器配置可能缺失`, false, true);
        }
    });
}

/**
 * 验证数据文件完整性
 */
function verifyDataFiles() {
    console.log('\n📊 验证数据文件完整性...');
    
    const dataFile = path.join(__dirname, '../config/malaysia-states-cities.json');
    if (!fs.existsSync(dataFile)) {
        logResult('❌ 马来西亚数据文件不存在', false);
        return;
    }
    
    try {
        const data = JSON.parse(fs.readFileSync(dataFile, 'utf8'));
        
        // 验证数据结构
        verificationResults.total++;
        if (data.states && Object.keys(data.states).length === 16) {
            logResult('✅ 州属数据完整 (16个州属)', true);
        } else {
            logResult('❌ 州属数据不完整', false);
        }
        
        verificationResults.total++;
        if (data.cities && Object.keys(data.cities).length === 16) {
            logResult('✅ 城市数据结构完整', true);
        } else {
            logResult('❌ 城市数据结构不完整', false);
        }
        
        // 验证特定测试用例数据
        const testCases = [
            { state: '01', city: '0109', name: 'Iskandar Puteri', chinese: '依斯干达公主城' },
            { state: '14', city: '1401', name: 'KLCC', chinese: '双峰塔' },
            { state: '07', city: '0700', name: 'George Town', chinese: '乔治市' }
        ];
        
        testCases.forEach(testCase => {
            verificationResults.total++;
            const stateData = data.cities[testCase.state];
            if (stateData && stateData.cities[testCase.city]) {
                const cityData = stateData.cities[testCase.city];
                if (cityData.name === testCase.name && cityData.chinese === testCase.chinese) {
                    logResult(`✅ 测试用例数据正确: ${testCase.name}`, true);
                } else {
                    logResult(`❌ 测试用例数据不匹配: ${testCase.name}`, false);
                }
            } else {
                logResult(`❌ 测试用例数据缺失: ${testCase.name}`, false);
            }
        });
        
    } catch (error) {
        logResult(`❌ 数据文件解析失败: ${error.message}`, false);
    }
}

/**
 * 验证AI配置文件
 */
function verifyAIConfiguration() {
    console.log('\n🤖 验证AI配置文件...');
    
    const aiConfigFile = path.join(__dirname, '../config/ai-config.js');
    if (!fs.existsSync(aiConfigFile)) {
        logResult('❌ AI配置文件不存在', false);
        return;
    }
    
    const aiConfigContent = fs.readFileSync(aiConfigFile, 'utf8');
    
    // 检查旅行信息解析配置
    verificationResults.total++;
    if (aiConfigContent.includes('TRAVEL_INFO_PARSING')) {
        logResult('✅ 旅行信息解析配置存在', true);
    } else {
        logResult('❌ 旅行信息解析配置缺失', false);
    }
    
    // 检查地理信息字段
    const geoFields = ['state', 'city', 'postcode'];
    geoFields.forEach(field => {
        verificationResults.total++;
        if (aiConfigContent.includes(field)) {
            logResult(`✅ AI配置包含${field}字段`, true);
        } else {
            logResult(`❌ AI配置缺少${field}字段`, false);
        }
    });
    
    // 检查Google Maps集成配置
    verificationResults.total++;
    if (aiConfigContent.includes('GOOGLE_MAPS_INTEGRATION')) {
        logResult('✅ Google Maps集成配置存在', true);
    } else {
        logResult('❌ Google Maps集成配置缺失', false);
    }
}

/**
 * 验证测试文件
 */
function verifyTestFiles() {
    console.log('\n🧪 验证测试文件...');
    
    const testFile = path.join(__dirname, 'traveling-info-test.html');
    verificationResults.total++;
    if (fs.existsSync(testFile)) {
        logResult('✅ 测试文件存在', true);
        
        const testContent = fs.readFileSync(testFile, 'utf8');
        
        // 检查测试用例
        const testCases = ['新山乐高乐园', 'KLCC', '槟城乔治市'];
        testCases.forEach(testCase => {
            verificationResults.total++;
            if (testContent.includes(testCase)) {
                logResult(`✅ 测试用例包含: ${testCase}`, true);
            } else {
                logResult(`❌ 测试用例缺失: ${testCase}`, false);
            }
        });
    } else {
        logResult('❌ 测试文件不存在', false);
    }
}

/**
 * 验证Google Maps集成文件
 */
function verifyGoogleMapsIntegration() {
    console.log('\n🗺️ 验证Google Maps集成...');
    
    const googleMapsFile = path.join(__dirname, '../modules/google-maps-integration.js');
    verificationResults.total++;
    if (fs.existsSync(googleMapsFile)) {
        logResult('✅ Google Maps集成文件存在', true);
        
        const content = fs.readFileSync(googleMapsFile, 'utf8');
        
        // 检查关键方法
        const keyMethods = ['standardizeAddress', 'mapToMDACFields', 'validateMalaysiaLocation'];
        keyMethods.forEach(method => {
            verificationResults.total++;
            if (content.includes(method)) {
                logResult(`✅ Google Maps方法存在: ${method}`, true);
            } else {
                logResult(`❌ Google Maps方法缺失: ${method}`, false);
            }
        });
    } else {
        logResult('❌ Google Maps集成文件不存在', false);
    }
}

/**
 * 记录验证结果
 */
function logResult(message, passed, isWarning = false) {
    console.log(message);
    if (isWarning) {
        verificationResults.warnings++;
    } else if (passed) {
        verificationResults.passed++;
    } else {
        verificationResults.failed++;
    }
}

/**
 * 显示验证总结
 */
function showVerificationSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 验证结果总结');
    console.log('='.repeat(60));
    
    const successRate = verificationResults.total > 0 ? 
        (verificationResults.passed / verificationResults.total * 100).toFixed(1) : 0;
    
    console.log(`总验证项目: ${verificationResults.total}`);
    console.log(`✅ 通过: ${verificationResults.passed}`);
    console.log(`❌ 失败: ${verificationResults.failed}`);
    console.log(`⚠️ 警告: ${verificationResults.warnings}`);
    console.log(`📈 成功率: ${successRate}%`);
    
    if (successRate >= 90) {
        console.log('\n🎉 验证结果: 优秀！所有关键功能都已正确实现。');
    } else if (successRate >= 80) {
        console.log('\n✅ 验证结果: 良好！大部分功能已正确实现，有少量问题需要修复。');
    } else if (successRate >= 70) {
        console.log('\n⚠️ 验证结果: 一般！存在一些问题需要修复。');
    } else {
        console.log('\n❌ 验证结果: 需要改进！存在较多问题需要修复。');
    }
    
    console.log('\n📋 下一步建议:');
    if (verificationResults.failed > 0) {
        console.log('1. 修复上述失败的验证项目');
        console.log('2. 重新运行验证脚本确认修复');
    }
    if (verificationResults.warnings > 0) {
        console.log('3. 检查警告项目，确保功能正常');
    }
    console.log('4. 运行完整的功能测试');
    console.log('5. 在实际环境中测试用户体验');
    
    console.log('\n🚀 准备部署状态:', successRate >= 85 ? '✅ 准备就绪' : '❌ 需要修复');
}

// 运行所有验证
async function runAllVerifications() {
    try {
        verifyHTMLStructure();
        verifyJavaScriptFunctions();
        verifyDataFiles();
        verifyAIConfiguration();
        verifyTestFiles();
        verifyGoogleMapsIntegration();
        
        showVerificationSummary();
        
    } catch (error) {
        console.error('❌ 验证过程中发生错误:', error);
        process.exit(1);
    }
}

// 启动验证
runAllVerifications();
