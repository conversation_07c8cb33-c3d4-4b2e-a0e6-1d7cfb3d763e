/**
 * MDAC表单数据验证和映射工具
 * 基于官方规格进行严格的数据验证和智能映射
 */

class MDACValidator {
    constructor() {
        this.loadCodeMappings();
    }

    /**
     * 加载代码映射数据
     */
    async loadCodeMappings() {
        try {
            // 加载完整的马来西亚州属和城市数据
            const response = await fetch(chrome.runtime.getURL('config/malaysia-states-cities.json'));
            const malaysiadata = await response.json();
            
            this.codeMappings = {
                nationality: {
                    'CHN': 'China', 'USA': 'United States', 'SGP': 'Singapore',
                    'MYS': 'Malaysia', 'GBR': 'United Kingdom', 'AUS': 'Australia',
                    'CAN': 'Canada', 'JPN': 'Japan', 'KOR': 'South Korea',
                    'THA': 'Thailand', 'IDN': 'Indonesia', 'VNM': 'Vietnam',
                    'PHL': 'Philippines', 'IND': 'India', 'FRA': 'France',
                    'DEU': 'Germany', 'ITA': 'Italy', 'ESP': 'Spain'
                },
                countryCode: {
                    '+86': 'CHN', '+60': 'MYS', '+65': 'SGP', '+1': 'USA',
                    '+44': 'GBR', '+61': 'AUS', '+81': 'JPN', '+82': 'KOR',
                    '+66': 'THA', '+62': 'IDN', '+84': 'VNM', '+63': 'PHL'
                },
                states: malaysiadata.states,
                cities: this.flattenCitiesData(malaysiadata.cities),
                cityMapping: malaysiadata.cityMapping,
                popularDestinations: malaysiadata.popularDestinations
            };
            
            console.log('✅ 马来西亚州属和城市数据加载完成');
        } catch (error) {
            console.error('❌ 加载代码映射失败:', error);
            // 降级到基础数据
            this.loadBasicMappings();
        }
    }

    /**
     * 将嵌套的城市数据扁平化为验证器使用的格式
     */
    flattenCitiesData(citiesData) {
        const flattened = {};
        
        Object.entries(citiesData).forEach(([stateCode, stateInfo]) => {
            if (stateInfo.cities) {
                Object.entries(stateInfo.cities).forEach(([cityCode, cityInfo]) => {
                    flattened[cityCode] = {
                        name: cityInfo.name,
                        state: stateCode,
                        chinese: cityInfo.chinese,
                        postcodeRange: cityInfo.postcode ? cityInfo.postcode.split('-') : null
                    };
                });
            }
        });
        
        return flattened;
    }

    /**
     * 基础映射数据作为降级选项
     */
    loadBasicMappings() {
        this.codeMappings = {
            nationality: {
                'CHN': 'China', 'USA': 'United States', 'SGP': 'Singapore',
                'MYS': 'Malaysia', 'GBR': 'United Kingdom', 'AUS': 'Australia'
            },
            countryCode: {
                '+86': 'CHN', '+60': 'MYS', '+65': 'SGP', '+1': 'USA'
            },
            states: {
                '01': 'Johor', '02': 'Kedah', '03': 'Kelantan', '04': 'Melaka',
                '05': 'Negeri Sembilan', '06': 'Pahang', '07': 'Pulau Pinang',
                '08': 'Perak', '09': 'Perlis', '10': 'Selangor',
                '11': 'Terengganu', '12': 'Sabah', '13': 'Sarawak',
                '14': 'Kuala Lumpur', '15': 'Labuan', '16': 'Putrajaya'
            },
            cities: {
                '1400': {name: 'Kuala Lumpur', state: '14'},
                '0700': {name: 'George Town', state: '07'},
                '0100': {name: 'Johor Bahru', state: '01'}
            }
        };
    }

    /**
     * 验证完整表单数据
     */
    validateFormData(formData) {
        const result = {
            isValid: true,
            errors: [],
            warnings: [],
            suggestions: [],
            completeness: 0
        };

        // 验证必填字段
        const requiredFields = [
            'name', 'passportNo', 'dateOfBirth', 'nationality', 'sex',
            'passportExpiry', 'email', 'countryCode', 'mobileNo',
            'arrivalDate', 'departureDate', 'flightNo', 'modeOfTravel',
            'lastPort', 'accommodation', 'address', 'state', 'postcode', 'city'
        ];

        let filledFields = 0;
        requiredFields.forEach(field => {
            if (formData[field] && formData[field].toString().trim()) {
                filledFields++;
            } else {
                result.errors.push(`必填字段 ${field} 缺失`);
                result.isValid = false;
            }
        });

        result.completeness = filledFields / requiredFields.length;

        // 验证各个字段
        this.validatePersonalInfo(formData, result);
        this.validateTravelInfo(formData, result);
        this.validateGeographicConsistency(formData, result);
        this.validateDateLogic(formData, result);

        return result;
    }

    /**
     * 验证个人信息字段
     */
    validatePersonalInfo(formData, result) {
        // 验证姓名
        if (formData.name) {
            if (!/^[A-Za-z\s,'-]+$/.test(formData.name)) {
                result.errors.push('姓名必须为英文字符');
                result.isValid = false;
            }
            if (formData.name.length > 50) {
                result.errors.push('姓名长度不能超过50个字符');
                result.isValid = false;
            }
        }

        // 验证护照号码
        if (formData.passportNo) {
            if (!/^[A-Za-z0-9]+$/.test(formData.passportNo)) {
                result.errors.push('护照号码格式不正确');
                result.isValid = false;
            }
            if (formData.passportNo.length > 15) {
                result.errors.push('护照号码长度不能超过15个字符');
                result.isValid = false;
            }
        }

        // 验证邮箱
        if (formData.email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(formData.email)) {
                result.errors.push('邮箱格式不正确');
                result.isValid = false;
            }
            if (formData.email.length > 100) {
                result.errors.push('邮箱长度不能超过100个字符');
                result.isValid = false;
            }
        }

        // 验证确认邮箱
        if (formData.confirmEmail && formData.email) {
            if (formData.confirmEmail !== formData.email) {
                result.errors.push('确认邮箱与邮箱不一致');
                result.isValid = false;
            }
        }

        // 验证手机号码
        if (formData.mobileNo) {
            if (!/^\d{7,15}$/.test(formData.mobileNo)) {
                result.errors.push('手机号码必须为7-15位数字');
                result.isValid = false;
            }
        }

        // 验证国籍代码
        if (formData.nationality) {
            if (!this.codeMappings.nationality[formData.nationality]) {
                result.errors.push('国籍代码不正确');
                result.isValid = false;
            }
        }

        // 验证性别
        if (formData.sex) {
            if (!['1', '2'].includes(formData.sex)) {
                result.errors.push('性别代码必须为1或2');
                result.isValid = false;
            }
        }

        // 验证国家代码
        if (formData.countryCode) {
            if (!this.codeMappings.countryCode[formData.countryCode]) {
                result.errors.push('国家代码不正确');
                result.isValid = false;
            }
        }
    }

    /**
     * 验证旅行信息字段
     */
    validateTravelInfo(formData, result) {
        // 验证航班号
        if (formData.flightNo) {
            if (!/^[A-Za-z]{2,3}\d{1,4}$/.test(formData.flightNo)) {
                result.warnings.push('航班号格式可能不正确，应为2-3位字母+1-4位数字');
            }
            if (formData.flightNo.length > 10) {
                result.errors.push('航班号长度不能超过10个字符');
                result.isValid = false;
            }
        }

        // 验证旅行方式
        if (formData.modeOfTravel) {
            if (!['AIR', 'LAND', 'SEA'].includes(formData.modeOfTravel)) {
                result.errors.push('旅行方式必须为AIR、LAND或SEA');
                result.isValid = false;
            }
        }

        // 验证住宿类型
        if (formData.accommodation) {
            if (!['01', '02', '03', '04', '05', '99'].includes(formData.accommodation)) {
                result.errors.push('住宿类型代码不正确');
                result.isValid = false;
            }
        }

        // 验证地址
        if (formData.address) {
            if (!/^[A-Za-z0-9\s,.-]+$/.test(formData.address)) {
                result.errors.push('地址必须为英文字符');
                result.isValid = false;
            }
            if (formData.address.length > 100) {
                result.errors.push('地址长度不能超过100个字符');
                result.isValid = false;
            }
        }

        // 验证邮政编码
        if (formData.postcode) {
            if (!/^\d{5}$/.test(formData.postcode)) {
                result.errors.push('邮政编码必须为5位数字');
                result.isValid = false;
            }
        }
    }

    /**
     * 验证地理位置一致性
     */
    validateGeographicConsistency(formData, result) {
        if (formData.state && formData.city && formData.postcode) {
            const cityInfo = this.codeMappings.cities[formData.city];
            
            if (cityInfo) {
                // 验证城市与州的一致性
                if (cityInfo.state !== formData.state) {
                    result.errors.push('城市代码与州代码不匹配');
                    result.isValid = false;
                }

                // 验证邮政编码范围
                const postcode = parseInt(formData.postcode);
                const minPostcode = parseInt(cityInfo.postcodeRange[0]);
                const maxPostcode = parseInt(cityInfo.postcodeRange[1]);
                
                if (postcode < minPostcode || postcode > maxPostcode) {
                    result.warnings.push('邮政编码可能与城市不匹配');
                }
            } else {
                result.errors.push('城市代码不正确');
                result.isValid = false;
            }
        }
    }

    /**
     * 验证日期逻辑
     */
    validateDateLogic(formData, result) {
        const today = new Date();
        
        // 验证出生日期
        if (formData.dateOfBirth) {
            const birthDate = this.parseDate(formData.dateOfBirth);
            if (birthDate && birthDate >= today) {
                result.errors.push('出生日期不能晚于当前日期');
                result.isValid = false;
            }
        }

        // 验证护照到期日期
        if (formData.passportExpiry) {
            const expiryDate = this.parseDate(formData.passportExpiry);
            if (expiryDate && expiryDate <= today) {
                result.errors.push('护照到期日期必须晚于当前日期');
                result.isValid = false;
            }
        }

        // 验证到达和离开日期
        if (formData.arrivalDate && formData.departureDate) {
            const arrivalDate = this.parseDate(formData.arrivalDate);
            const departureDate = this.parseDate(formData.departureDate);
            
            if (arrivalDate && departureDate) {
                if (arrivalDate >= departureDate) {
                    result.errors.push('到达日期必须早于离开日期');
                    result.isValid = false;
                }

                // 检查停留时间是否合理（通常不超过90天）
                const stayDays = (departureDate - arrivalDate) / (1000 * 60 * 60 * 24);
                if (stayDays > 90) {
                    result.warnings.push('停留时间超过90天，请确认是否正确');
                }
            }
        }
    }

    /**
     * 解析日期字符串 (DD/MM/YYYY格式)
     */
    parseDate(dateString) {
        if (!dateString) return null;
        
        const parts = dateString.split('/');
        if (parts.length !== 3) return null;
        
        const day = parseInt(parts[0]);
        const month = parseInt(parts[1]) - 1; // JavaScript月份从0开始
        const year = parseInt(parts[2]);
        
        return new Date(year, month, day);
    }

    /**
     * 智能代码匹配
     */
    smartCodeMapping(value, type) {
        if (!value || !this.codeMappings[type]) return null;

        const mappings = this.codeMappings[type];
        const lowerValue = value.toLowerCase();

        // 精确匹配
        for (const [code, name] of Object.entries(mappings)) {
            if (code.toLowerCase() === lowerValue || 
                (typeof name === 'string' && name.toLowerCase() === lowerValue)) {
                return code;
            }
        }

        // 模糊匹配
        for (const [code, name] of Object.entries(mappings)) {
            if (typeof name === 'string' && 
                (name.toLowerCase().includes(lowerValue) || 
                 lowerValue.includes(name.toLowerCase()))) {
                return code;
            }
        }

        return null;
    }

    /**
     * 智能州属匹配（增强版）
     */
    smartStateMapping(value) {
        if (!value) return null;
        
        const cleanValue = value.toString().trim();
        const states = this.codeMappings.states;
        
        // 直接代码匹配
        if (states[cleanValue]) {
            return cleanValue;
        }
        
        // 州属名称映射（支持中英文）
        const stateNameMapping = {
            'johor': '01', '柔佛': '01',
            'kedah': '02', '吉打': '02',
            'kelantan': '03', '吉兰丹': '03',
            'melaka': '04', 'malacca': '04', '马六甲': '04',
            'negeri sembilan': '05', '森美兰': '05',
            'pahang': '06', '彭亨': '06',
            'pulau pinang': '07', 'penang': '07', '槟城': '07',
            'perak': '08', '霹雳': '08',
            'perlis': '09', '玻璃市': '09',
            'selangor': '10', '雪兰莪': '10',
            'terengganu': '11', '登嘉楼': '11',
            'sabah': '12', '沙巴': '12',
            'sarawak': '13', '砂拉越': '13',
            'kuala lumpur': '14', 'kl': '14', '吉隆坡': '14',
            'labuan': '15', '纳闽': '15',
            'putrajaya': '16', '布城': '16'
        };
        
        const lowerValue = cleanValue.toLowerCase();
        return stateNameMapping[lowerValue] || null;
    }

    /**
     * 智能城市匹配（支持中英文和模糊匹配）
     */
    smartCityMapping(value, stateCode = null) {
        if (!value) return null;
        
        const cleanValue = value.toString().trim();
        const cities = this.codeMappings.cities;
        
        // 直接代码匹配
        if (cities[cleanValue]) {
            return cleanValue;
        }
        
        // 精确名称匹配
        for (const [code, cityInfo] of Object.entries(cities)) {
            if (cityInfo.name && cityInfo.name.toLowerCase() === cleanValue.toLowerCase()) {
                if (!stateCode || cityInfo.state === stateCode) {
                    return code;
                }
            }
            // 中文名称匹配
            if (cityInfo.chinese && cityInfo.chinese === cleanValue) {
                if (!stateCode || cityInfo.state === stateCode) {
                    return code;
                }
            }
        }
        
        // 模糊匹配（部分字符串匹配）
        for (const [code, cityInfo] of Object.entries(cities)) {
            if (cityInfo.name && (cityInfo.name.toLowerCase().includes(cleanValue.toLowerCase()) ||
                cleanValue.toLowerCase().includes(cityInfo.name.toLowerCase()))) {
                if (!stateCode || cityInfo.state === stateCode) {
                    return code;
                }
            }
            if (cityInfo.chinese && (cityInfo.chinese.includes(cleanValue) || 
                cleanValue.includes(cityInfo.chinese))) {
                if (!stateCode || cityInfo.state === stateCode) {
                    return code;
                }
            }
        }
        
        return null;
    }

    /**
     * 根据邮政编码推断城市
     */
    getCityByPostcode(postcode) {
        if (!postcode || !this.codeMappings.cities) return null;
        
        const postcodeNum = parseInt(postcode.toString().replace(/\D/g, ''));
        if (isNaN(postcodeNum)) return null;
        
        for (const [code, cityInfo] of Object.entries(this.codeMappings.cities)) {
            if (cityInfo.postcodeRange) {
                const ranges = cityInfo.postcodeRange;
                if (Array.isArray(ranges) && ranges.length >= 2) {
                    const [min, max] = ranges;
                    if (postcodeNum >= parseInt(min) && postcodeNum <= parseInt(max)) {
                        return code;
                    }
                } else if (typeof ranges === 'string') {
                    // 处理 "50000-60000" 格式
                    const [min, max] = ranges.split('-').map(s => parseInt(s));
                    if (postcodeNum >= min && postcodeNum <= max) {
                        return code;
                    }
                }
            }
        }
        
        return null;
    }

    /**
     * 获取州属下的所有城市
     */
    getCitiesByState(stateCode) {
        if (!stateCode || !this.codeMappings.cities) return [];
        
        const cities = [];
        Object.entries(this.codeMappings.cities).forEach(([code, cityInfo]) => {
            if (cityInfo.state === stateCode) {
                cities.push({
                    code: code,
                    name: cityInfo.name,
                    chinese: cityInfo.chinese || '',
                    postcode: cityInfo.postcodeRange
                });
            }
        });
        
        return cities.sort((a, b) => a.name.localeCompare(b.name));
    }

    /**
     * 获取城市详细信息
     */
    getCityDetails(cityCode) {
        if (!this.codeMappings.cities || !cityCode) return null;
        
        const cityInfo = this.codeMappings.cities[cityCode];
        if (!cityInfo) return null;
        
        const stateCode = cityInfo.state;
        const stateName = this.codeMappings.states[stateCode];
        
        return {
            code: cityCode,
            name: cityInfo.name,
            chinese: cityInfo.chinese,
            postcode: cityInfo.postcodeRange ? cityInfo.postcodeRange.join('-') : 'N/A',
            stateCode: stateCode,
            stateName: stateName,
            displayName: `${cityInfo.name} (${cityInfo.chinese})`,
            fullDisplay: `${cityInfo.name} (${cityInfo.chinese}) - ${stateName} - ${cityInfo.postcodeRange ? cityInfo.postcodeRange.join('-') : 'N/A'}`
        };
    }

    /**
     * 获取所有城市的详细信息（用于下拉列表等）
     */
    getAllCitiesWithDetails() {
        if (!this.codeMappings.cities) return [];
        
        const cities = [];
        Object.entries(this.codeMappings.cities).forEach(([cityCode, cityInfo]) => {
            const stateCode = cityInfo.state;
            const stateName = this.codeMappings.states[stateCode];
            
            cities.push({
                code: cityCode,
                name: cityInfo.name,
                chinese: cityInfo.chinese,
                postcode: cityInfo.postcodeRange ? cityInfo.postcodeRange.join('-') : 'N/A',
                stateCode: stateCode,
                stateName: stateName,
                displayName: `${cityInfo.name} (${cityInfo.chinese})`,
                fullDisplay: `${cityInfo.name} (${cityInfo.chinese}) - ${stateName}`,
                searchText: `${cityInfo.name} ${cityInfo.chinese} ${stateName}`.toLowerCase()
            });
        });
        
        // 按州属和城市名称排序
        return cities.sort((a, b) => {
            if (a.stateCode !== b.stateCode) {
                return a.stateCode.localeCompare(b.stateCode);
            }
            return a.name.localeCompare(b.name);
        });
    }

    /**
     * 获取州属下的城市详细信息
     */
    getCitiesByStateWithDetails(stateCode) {
        if (!stateCode || !this.codeMappings.cities) return [];
        
        const cities = [];
        Object.entries(this.codeMappings.cities).forEach(([cityCode, cityInfo]) => {
            if (cityInfo.state === stateCode) {
                cities.push({
                    code: cityCode,
                    name: cityInfo.name,
                    chinese: cityInfo.chinese,
                    postcode: cityInfo.postcodeRange ? cityInfo.postcodeRange.join('-') : 'N/A',
                    displayName: `${cityInfo.name} (${cityInfo.chinese})`,
                    fullDisplay: `${cityInfo.name} (${cityInfo.chinese}) - ${cityInfo.postcodeRange ? cityInfo.postcodeRange.join('-') : 'N/A'}`
                });
            }
        });
        
        return cities.sort((a, b) => a.name.localeCompare(b.name));
    }

    /**
     * 搜索城市（支持模糊搜索）
     */
    searchCities(query, limit = 10) {
        if (!query || query.trim().length < 1) return [];
        
        const searchQuery = query.toLowerCase().trim();
        const allCities = this.getAllCitiesWithDetails();
        
        const matches = allCities.filter(city => 
            city.searchText.includes(searchQuery) ||
            city.name.toLowerCase().includes(searchQuery) ||
            city.chinese.includes(searchQuery) ||
            city.stateName.toLowerCase().includes(searchQuery)
        );
        
        return matches.slice(0, limit);
    }

    /**
     * 获取热门城市详细信息
     */
    getPopularDestinationsWithDetails() {
        if (!this.codeMappings.popularDestinations) return [];
        
        return this.codeMappings.popularDestinations.tourist_cities.map(dest => {
            const cityDetails = this.getCityDetails(dest.code);
            return {
                ...dest,
                ...cityDetails,
                reason: dest.reason
            };
        });
    }
}

// 导出验证器
if (typeof window !== 'undefined') {
    window.MDACValidator = MDACValidator;
} else if (typeof module !== 'undefined' && module.exports) {
    module.exports = MDACValidator;
}
