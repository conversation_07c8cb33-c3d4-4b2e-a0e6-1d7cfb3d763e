/**
 * MDAC AI智能分析工具 - 智能表单字段检测系统
 * 动态检测和映射MDAC网站的表单字段，替换硬编码映射
 */

class FormFieldDetector {
    constructor() {
        // 字段检测模式配置
        this.fieldPatterns = {
            // 姓名字段模式
            name: {
                ids: ['name', 'fullname', 'applicant', 'applicantName', 'full_name'],
                names: ['name', 'fullname', 'applicant', 'applicantName'],
                classes: ['name-field', 'applicant-name', 'full-name'],
                placeholders: ['姓名', 'name', 'full name', '英文姓名', 'applicant name'],
                labels: ['姓名', 'name', 'full name', '申请人姓名', 'applicant name'],
                types: ['text'],
                priority: 10
            },
            
            // 护照号码字段模式
            passportNo: {
                ids: ['passNo', 'passport', 'passportNo', 'passport_no', 'pass_no'],
                names: ['passNo', 'passport', 'passportNo', 'passport_no'],
                classes: ['passport-field', 'pass-no', 'passport-number'],
                placeholders: ['护照号码', 'passport', 'passport number', '护照号'],
                labels: ['护照号码', 'passport', 'passport number', '护照号'],
                types: ['text'],
                priority: 10
            },
            
            // 出生日期字段模式
            dateOfBirth: {
                ids: ['dob', 'dateOfBirth', 'birth_date', 'birthDate'],
                names: ['dob', 'dateOfBirth', 'birth_date', 'birthDate'],
                classes: ['dob-field', 'birth-date', 'date-of-birth'],
                placeholders: ['出生日期', 'date of birth', 'dob', 'DD/MM/YYYY'],
                labels: ['出生日期', 'date of birth', 'dob', '生日'],
                types: ['text', 'date'],
                priority: 9
            },
            
            // 国籍字段模式
            nationality: {
                ids: ['nationality', 'country', 'nation', 'countryCode'],
                names: ['nationality', 'country', 'nation', 'countryCode'],
                classes: ['nationality-field', 'country-select', 'nation'],
                placeholders: ['国籍', 'nationality', 'country'],
                labels: ['国籍', 'nationality', 'country', '国家'],
                types: ['select-one', 'text'],
                priority: 8
            },
            
            // 性别字段模式
            sex: {
                ids: ['sex', 'gender', 'sexCode'],
                names: ['sex', 'gender', 'sexCode'],
                classes: ['sex-field', 'gender-select'],
                placeholders: ['性别', 'sex', 'gender'],
                labels: ['性别', 'sex', 'gender'],
                types: ['select-one', 'radio'],
                priority: 7
            },
            
            // 护照到期日期字段模式
            passportExpiry: {
                ids: ['passExpiry', 'passportExpiry', 'passport_expiry', 'expiryDate'],
                names: ['passExpiry', 'passportExpiry', 'passport_expiry'],
                classes: ['passport-expiry', 'expiry-date'],
                placeholders: ['护照到期日', 'passport expiry', 'expiry date'],
                labels: ['护照到期日', 'passport expiry', 'expiry date'],
                types: ['text', 'date'],
                priority: 8
            },
            
            // 电子邮箱字段模式
            email: {
                ids: ['email', 'emailAddress', 'email_address', 'mail'],
                names: ['email', 'emailAddress', 'email_address'],
                classes: ['email-field', 'email-input'],
                placeholders: ['电子邮箱', 'email', 'email address', '邮箱'],
                labels: ['电子邮箱', 'email', 'email address', '邮箱'],
                types: ['email', 'text'],
                priority: 9
            },
            
            // 确认邮箱字段模式
            confirmEmail: {
                ids: ['confirmEmail', 'confirm_email', 'emailConfirm', 'email2'],
                names: ['confirmEmail', 'confirm_email', 'emailConfirm'],
                classes: ['confirm-email', 'email-confirm'],
                placeholders: ['确认邮箱', 'confirm email', 'email confirmation'],
                labels: ['确认邮箱', 'confirm email', 'email confirmation'],
                types: ['email', 'text'],
                priority: 8
            },
            
            // 国家代码字段模式
            countryCode: {
                ids: ['countryCode', 'country_code', 'phoneCountry', 'dialCode'],
                names: ['countryCode', 'country_code', 'phoneCountry'],
                classes: ['country-code', 'phone-country', 'dial-code'],
                placeholders: ['+86', '+60', '+1', 'country code'],
                labels: ['国家代码', 'country code', '区号'],
                types: ['select-one', 'text'],
                priority: 7
            },
            
            // 手机号码字段模式
            mobileNo: {
                ids: ['mobileNo', 'mobile', 'phone', 'phoneNumber', 'mobile_no'],
                names: ['mobileNo', 'mobile', 'phone', 'phoneNumber'],
                classes: ['mobile-field', 'phone-field', 'mobile-number'],
                placeholders: ['手机号码', 'mobile', 'phone number', '电话'],
                labels: ['手机号码', 'mobile', 'phone number', '电话'],
                types: ['tel', 'text'],
                priority: 9
            },
            
            // 到达日期字段模式
            arrivalDate: {
                ids: ['arrivalDate', 'arrival_date', 'arriveDate', 'entryDate'],
                names: ['arrivalDate', 'arrival_date', 'arriveDate'],
                classes: ['arrival-date', 'entry-date'],
                placeholders: ['到达日期', 'arrival date', 'entry date'],
                labels: ['到达日期', 'arrival date', 'entry date'],
                types: ['text', 'date'],
                priority: 9
            },
            
            // 离开日期字段模式
            departureDate: {
                ids: ['departureDate', 'departure_date', 'leaveDate', 'exitDate'],
                names: ['departureDate', 'departure_date', 'leaveDate'],
                classes: ['departure-date', 'exit-date'],
                placeholders: ['离开日期', 'departure date', 'exit date'],
                labels: ['离开日期', 'departure date', 'exit date'],
                types: ['text', 'date'],
                priority: 9
            },
            
            // 航班号字段模式
            flightNo: {
                ids: ['vesselNm', 'flightNo', 'flight_no', 'flightNumber', 'vessel'],
                names: ['vesselNm', 'flightNo', 'flight_no', 'flightNumber'],
                classes: ['flight-field', 'vessel-field', 'flight-number'],
                placeholders: ['航班号', 'flight number', 'vessel', '班次'],
                labels: ['航班号', 'flight number', 'vessel', '交通工具'],
                types: ['text'],
                priority: 8
            },
            
            // 旅行方式字段模式
            modeOfTravel: {
                ids: ['trvlMode', 'travelMode', 'travel_mode', 'modeOfTravel'],
                names: ['trvlMode', 'travelMode', 'travel_mode'],
                classes: ['travel-mode', 'mode-field'],
                placeholders: ['旅行方式', 'travel mode', 'mode of travel'],
                labels: ['旅行方式', 'travel mode', 'mode of travel'],
                types: ['select-one'],
                priority: 7
            },
            
            // 最后港口字段模式
            lastPort: {
                ids: ['embark', 'lastPort', 'last_port', 'embarkation'],
                names: ['embark', 'lastPort', 'last_port'],
                classes: ['embark-field', 'last-port'],
                placeholders: ['最后港口', 'last port', 'embarkation'],
                labels: ['最后港口', 'last port', 'embarkation'],
                types: ['select-one', 'text'],
                priority: 7
            },
            
            // 住宿类型字段模式
            accommodation: {
                ids: ['accommodationStay', 'accommodation', 'stay_type', 'lodging'],
                names: ['accommodationStay', 'accommodation', 'stay_type'],
                classes: ['accommodation-field', 'stay-type'],
                placeholders: ['住宿类型', 'accommodation', 'stay type'],
                labels: ['住宿类型', 'accommodation', 'stay type'],
                types: ['select-one'],
                priority: 7
            },
            
            // 地址行1字段模式
            address: {
                ids: ['accommodationAddress1', 'address1', 'address', 'street'],
                names: ['accommodationAddress1', 'address1', 'address'],
                classes: ['address-field', 'address1', 'street-address'],
                placeholders: ['地址', 'address', 'street address', '住址'],
                labels: ['地址', 'address', 'street address', '住址'],
                types: ['text'],
                priority: 8
            },
            
            // 地址行2字段模式
            address2: {
                ids: ['accommodationAddress2', 'address2', 'address_2'],
                names: ['accommodationAddress2', 'address2', 'address_2'],
                classes: ['address2-field', 'address2'],
                placeholders: ['地址2', 'address 2', 'address line 2'],
                labels: ['地址2', 'address 2', 'address line 2'],
                types: ['text'],
                priority: 6
            },
            
            // 州/省字段模式
            state: {
                ids: ['accommodationState', 'state', 'province', 'region'],
                names: ['accommodationState', 'state', 'province'],
                classes: ['state-field', 'province-field'],
                placeholders: ['州/省', 'state', 'province'],
                labels: ['州/省', 'state', 'province'],
                types: ['select-one', 'text'],
                priority: 8
            },
            
            // 邮政编码字段模式
            postcode: {
                ids: ['accommodationPostcode', 'postcode', 'postal_code', 'zip'],
                names: ['accommodationPostcode', 'postcode', 'postal_code'],
                classes: ['postcode-field', 'postal-code', 'zip-code'],
                placeholders: ['邮政编码', 'postcode', 'postal code', 'zip'],
                labels: ['邮政编码', 'postcode', 'postal code', 'zip'],
                types: ['text'],
                priority: 8
            },
            
            // 城市字段模式
            city: {
                ids: ['accommodationCity', 'city', 'town', 'municipality'],
                names: ['accommodationCity', 'city', 'town'],
                classes: ['city-field', 'town-field'],
                placeholders: ['城市', 'city', 'town'],
                labels: ['城市', 'city', 'town'],
                types: ['select-one', 'text'],
                priority: 8
            }
        };
        
        // 检测结果缓存
        this.detectionCache = new Map();
        
        // 检测统计
        this.detectionStats = {
            totalDetections: 0,
            successfulDetections: 0,
            failedDetections: 0,
            cacheHits: 0
        };
    }
    
    /**
     * 检测页面中的所有表单字段
     * @returns {Object} 检测到的字段映射
     */
    async detectFormFields() {
        console.log('🔍 开始智能表单字段检测...');
        
        // 检查缓存
        const pageSignature = this.generatePageSignature();
        if (this.detectionCache.has(pageSignature)) {
            this.detectionStats.cacheHits++;
            console.log('📋 使用缓存的字段检测结果');
            return this.detectionCache.get(pageSignature);
        }
        
        const detectedFields = {};
        const allFormElements = this.getAllFormElements();
        
        console.log(`📝 发现 ${allFormElements.length} 个表单元素`);
        
        // 为每个字段类型进行检测
        for (const [fieldType, patterns] of Object.entries(this.fieldPatterns)) {
            const detectedElement = this.detectFieldByPatterns(fieldType, patterns, allFormElements);
            if (detectedElement) {
                detectedFields[fieldType] = detectedElement;
                this.detectionStats.successfulDetections++;
                console.log(`✅ 检测到字段: ${fieldType} -> ${detectedElement.id || detectedElement.name}`);
            } else {
                this.detectionStats.failedDetections++;
                console.log(`❌ 未检测到字段: ${fieldType}`);
            }
        }
        
        this.detectionStats.totalDetections++;
        
        // 缓存结果
        this.detectionCache.set(pageSignature, detectedFields);
        
        console.log(`🎯 字段检测完成: ${this.detectionStats.successfulDetections}/${Object.keys(this.fieldPatterns).length} 个字段`);
        
        return detectedFields;
    }
    
    /**
     * 获取页面中所有表单元素
     * @returns {Array} 表单元素数组
     */
    getAllFormElements() {
        const selectors = [
            'input[type="text"]',
            'input[type="email"]', 
            'input[type="tel"]',
            'input[type="date"]',
            'input[type="password"]',
            'input[type="radio"]',
            'input[type="checkbox"]',
            'select',
            'textarea'
        ];
        
        const elements = [];
        selectors.forEach(selector => {
            elements.push(...document.querySelectorAll(selector));
        });
        
        return elements;
    }
    
    /**
     * 根据模式检测特定字段
     * @param {string} fieldType 字段类型
     * @param {Object} patterns 匹配模式
     * @param {Array} elements 待检测的元素数组
     * @returns {Element|null} 检测到的元素
     */
    detectFieldByPatterns(fieldType, patterns, elements) {
        let bestMatch = null;
        let bestScore = 0;
        
        for (const element of elements) {
            const score = this.calculateMatchScore(element, patterns);
            if (score > bestScore && score > 0) {
                bestScore = score;
                bestMatch = element;
            }
        }
        
        // 只返回得分足够高的匹配
        return bestScore >= 3 ? bestMatch : null;
    }
    
    /**
     * 计算元素与模式的匹配分数
     * @param {Element} element 待检测元素
     * @param {Object} patterns 匹配模式
     * @returns {number} 匹配分数
     */
    calculateMatchScore(element, patterns) {
        let score = 0;
        
        // ID匹配 (权重: 5)
        if (element.id && patterns.ids) {
            for (const pattern of patterns.ids) {
                if (element.id.toLowerCase().includes(pattern.toLowerCase())) {
                    score += 5;
                    break;
                }
            }
        }
        
        // Name属性匹配 (权重: 4)
        if (element.name && patterns.names) {
            for (const pattern of patterns.names) {
                if (element.name.toLowerCase().includes(pattern.toLowerCase())) {
                    score += 4;
                    break;
                }
            }
        }
        
        // Class匹配 (权重: 3)
        if (element.className && patterns.classes) {
            for (const pattern of patterns.classes) {
                if (element.className.toLowerCase().includes(pattern.toLowerCase())) {
                    score += 3;
                    break;
                }
            }
        }
        
        // Placeholder匹配 (权重: 2)
        if (element.placeholder && patterns.placeholders) {
            for (const pattern of patterns.placeholders) {
                if (element.placeholder.toLowerCase().includes(pattern.toLowerCase())) {
                    score += 2;
                    break;
                }
            }
        }
        
        // Label匹配 (权重: 2)
        const label = this.findAssociatedLabel(element);
        if (label && patterns.labels) {
            for (const pattern of patterns.labels) {
                if (label.toLowerCase().includes(pattern.toLowerCase())) {
                    score += 2;
                    break;
                }
            }
        }
        
        // 类型匹配 (权重: 1)
        if (patterns.types && patterns.types.includes(element.type)) {
            score += 1;
        }
        
        return score;
    }
    
    /**
     * 查找元素关联的标签文本
     * @param {Element} element 表单元素
     * @returns {string|null} 标签文本
     */
    findAssociatedLabel(element) {
        // 通过for属性查找
        if (element.id) {
            const label = document.querySelector(`label[for="${element.id}"]`);
            if (label) return label.textContent.trim();
        }
        
        // 查找父级label
        let parent = element.parentElement;
        while (parent && parent.tagName !== 'FORM') {
            if (parent.tagName === 'LABEL') {
                return parent.textContent.trim();
            }
            parent = parent.parentElement;
        }
        
        // 查找前面的文本节点或标签
        const prevElement = element.previousElementSibling;
        if (prevElement && (prevElement.tagName === 'LABEL' || prevElement.tagName === 'SPAN')) {
            return prevElement.textContent.trim();
        }
        
        return null;
    }
    
    /**
     * 生成页面签名用于缓存
     * @returns {string} 页面签名
     */
    generatePageSignature() {
        const forms = document.querySelectorAll('form');
        let signature = window.location.pathname;
        
        forms.forEach(form => {
            signature += form.innerHTML.length;
        });
        
        return btoa(signature).substring(0, 16);
    }
    
    /**
     * 获取检测统计信息
     * @returns {Object} 统计信息
     */
    getDetectionStats() {
        return {
            ...this.detectionStats,
            successRate: this.detectionStats.totalDetections > 0 
                ? (this.detectionStats.successfulDetections / (this.detectionStats.successfulDetections + this.detectionStats.failedDetections) * 100).toFixed(2)
                : 0,
            cacheHitRate: this.detectionStats.totalDetections > 0
                ? (this.detectionStats.cacheHits / this.detectionStats.totalDetections * 100).toFixed(2)
                : 0
        };
    }
    
    /**
     * 清除检测缓存
     */
    clearCache() {
        this.detectionCache.clear();
        console.log('🗑️ 字段检测缓存已清除');
    }
    
    /**
     * 验证检测结果的准确性
     * @param {Object} detectedFields 检测到的字段
     * @returns {Object} 验证结果
     */
    validateDetection(detectedFields) {
        const validation = {
            isValid: true,
            missingCriticalFields: [],
            duplicateFields: [],
            confidence: 0
        };
        
        // 检查关键字段
        const criticalFields = ['name', 'passportNo', 'email', 'mobileNo'];
        for (const field of criticalFields) {
            if (!detectedFields[field]) {
                validation.missingCriticalFields.push(field);
                validation.isValid = false;
            }
        }
        
        // 检查重复字段
        const elementIds = new Set();
        for (const [fieldType, element] of Object.entries(detectedFields)) {
            const elementId = element.id || element.name || element.className;
            if (elementIds.has(elementId)) {
                validation.duplicateFields.push(fieldType);
                validation.isValid = false;
            }
            elementIds.add(elementId);
        }
        
        // 计算置信度
        const detectedCount = Object.keys(detectedFields).length;
        const totalFields = Object.keys(this.fieldPatterns).length;
        validation.confidence = (detectedCount / totalFields * 100).toFixed(2);
        
        return validation;
    }
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FormFieldDetector;
} else {
    window.FormFieldDetector = FormFieldDetector;
}
