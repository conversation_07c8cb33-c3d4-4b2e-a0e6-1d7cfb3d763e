/**
 * MDAC AI智能分析工具 - Google Maps API集成模块
 * 实现地址标准化、验证和邮政编码自动补全功能
 */

class GoogleMapsIntegration {
    constructor(apiKey = null) {
        // 使用现有的Gemini API密钥或传入的API密钥
        this.apiKey = apiKey || (typeof GEMINI_CONFIG !== 'undefined' ? GEMINI_CONFIG.DEFAULT_API_KEY : null);
        
        // API配置
        this.config = {
            geocodingUrl: 'https://maps.googleapis.com/maps/api/geocode/json',
            placesUrl: 'https://maps.googleapis.com/maps/api/place/autocomplete/json',
            detailsUrl: 'https://maps.googleapis.com/maps/api/place/details/json',
            rateLimitDelay: 100, // API调用间隔（毫秒）
            maxRetries: 3,
            timeout: 10000
        };
        
        // 马来西亚边界
        this.malaysiaBounds = {
            northeast: { lat: 7.363417, lng: 119.267578 },
            southwest: { lat: 0.855674, lng: 99.643478 }
        };
        
        // 缓存配置
        this.cache = new Map();
        this.cacheConfig = {
            maxSize: 100,
            ttl: 3600000 // 1小时
        };
        
        // 统计信息
        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            cacheHits: 0,
            apiCalls: 0,
            averageResponseTime: 0
        };
        
        // 马来西亚州邮编范围
        this.statePostcodeRanges = {
            '01': [80000, 86999], // Johor
            '02': [5000, 9999], // Kedah - 修复前导零问题
            '03': [15000, 18999], // Kelantan
            '04': [75000, 78999], // Melaka
            '05': [70000, 73999], // Negeri Sembilan
            '06': [25000, 28999], // Pahang
            '07': [10000, 14999], // Penang
            '08': [30000, 36999], // Perak
            '09': [1000, 2999], // Perlis - 修复前导零问题
            '10': [88000, 91999], // Sabah
            '11': [93000, 98999], // Sarawak
            '12': [40000, 48999], // Selangor
            '13': [20000, 24999], // Terengganu
            '14': [50000, 60999], // Kuala Lumpur
            '15': [87000, 87999], // Labuan
            '16': [62000, 62999]  // Putrajaya
        };
    }
    
    /**
     * 地址标准化主函数
     * @param {string} address - 输入地址
     * @param {Object} options - 选项配置
     * @returns {Promise<Object>} 标准化结果
     */
    async standardizeAddress(address, options = {}) {
        const startTime = Date.now();
        this.stats.totalRequests++;
        
        try {
            // 检查缓存
            const cacheKey = `standardize_${address}`;
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                this.stats.cacheHits++;
                return cached;
            }
            
            // 清理输入地址
            const cleanAddress = this.cleanAddress(address);
            
            // 调用Google Maps Geocoding API
            const geocodeResult = await this.geocodeAddress(cleanAddress);
            
            if (!geocodeResult.success) {
                throw new Error(geocodeResult.error);
            }
            
            // 验证地址是否在马来西亚
            const locationValidation = this.validateMalaysiaLocation(geocodeResult.data);
            
            // 提取地址组件
            const components = this.extractAddressComponents(geocodeResult.data);
            
            // 映射到MDAC字段
            const mdacMapping = this.mapToMDACFields(components);
            
            // 质量评估
            const quality = this.assessAddressQuality(components, locationValidation);
            
            // 生成建议
            const suggestions = this.generateSuggestions(components, quality);
            
            const result = {
                success: true,
                originalAddress: address,
                standardizedAddress: this.formatStandardAddress(components),
                mdacMapping: mdacMapping,
                components: components,
                quality: quality,
                validation: locationValidation,
                suggestions: suggestions,
                responseTime: Date.now() - startTime
            };
            
            // 缓存结果
            this.setCache(cacheKey, result);
            
            this.stats.successfulRequests++;
            this.updateAverageResponseTime(Date.now() - startTime);
            
            return result;
            
        } catch (error) {
            this.stats.failedRequests++;
            return {
                success: false,
                error: error.message,
                originalAddress: address,
                responseTime: Date.now() - startTime
            };
        }
    }
    
    /**
     * 调用Google Maps Geocoding API
     */
    async geocodeAddress(address) {
        try {
            this.stats.apiCalls++;
            
            const url = new URL(this.config.geocodingUrl);
            url.searchParams.append('address', address);
            url.searchParams.append('key', this.apiKey);
            url.searchParams.append('region', 'MY'); // 偏向马来西亚结果
            url.searchParams.append('language', 'en'); // 英文结果
            
            const response = await fetch(url.toString(), {
                method: 'GET',
                timeout: this.config.timeout
            });
            
            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.status !== 'OK') {
                throw new Error(`Geocoding失败: ${data.status} - ${data.error_message || '未知错误'}`);
            }
            
            if (!data.results || data.results.length === 0) {
                throw new Error('未找到匹配的地址');
            }
            
            return {
                success: true,
                data: data.results[0] // 返回最佳匹配结果
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * 验证地址是否在马来西亚境内
     */
    validateMalaysiaLocation(geocodeData) {
        const location = geocodeData.geometry.location;
        const addressComponents = geocodeData.address_components;
        
        // 检查坐标是否在马来西亚边界内
        const inBounds = location.lat >= this.malaysiaBounds.southwest.lat &&
                        location.lat <= this.malaysiaBounds.northeast.lat &&
                        location.lng >= this.malaysiaBounds.southwest.lng &&
                        location.lng <= this.malaysiaBounds.northeast.lng;
        
        // 检查地址组件中是否包含马来西亚
        const hasCountry = addressComponents.some(component => 
            component.types.includes('country') && 
            (component.short_name === 'MY' || component.long_name === 'Malaysia')
        );
        
        return {
            inMalaysia: inBounds && hasCountry,
            coordinates: location,
            boundaryCheck: inBounds,
            countryCheck: hasCountry,
            confidence: (inBounds && hasCountry) ? 1.0 : (inBounds || hasCountry) ? 0.5 : 0.0
        };
    }
    
    /**
     * 提取地址组件
     */
    extractAddressComponents(geocodeData) {
        const components = {
            streetNumber: '',
            streetName: '',
            sublocality: '',
            locality: '',
            administrativeArea1: '', // 州
            administrativeArea2: '', // 区
            postalCode: '',
            country: '',
            formattedAddress: geocodeData.formatted_address
        };
        
        geocodeData.address_components.forEach(component => {
            const types = component.types;
            
            if (types.includes('street_number')) {
                components.streetNumber = component.long_name;
            } else if (types.includes('route')) {
                components.streetName = component.long_name;
            } else if (types.includes('sublocality') || types.includes('sublocality_level_1')) {
                components.sublocality = component.long_name;
            } else if (types.includes('locality')) {
                components.locality = component.long_name;
            } else if (types.includes('administrative_area_level_1')) {
                components.administrativeArea1 = component.long_name;
            } else if (types.includes('administrative_area_level_2')) {
                components.administrativeArea2 = component.long_name;
            } else if (types.includes('postal_code')) {
                components.postalCode = component.long_name;
            } else if (types.includes('country')) {
                components.country = component.long_name;
            }
        });
        
        return components;
    }
    
    /**
     * 映射到MDAC字段
     */
    mapToMDACFields(components) {
        // 构建地址行1
        let address1 = '';
        if (components.streetNumber) {
            address1 += components.streetNumber + ' ';
        }
        if (components.streetName) {
            address1 += components.streetName;
        }
        
        // 构建地址行2
        let address2 = '';
        if (components.sublocality) {
            address2 = components.sublocality;
        }
        
        // 映射州代码
        const stateCode = this.mapStateToCode(components.administrativeArea1);
        
        // 映射城市代码
        const cityCode = this.mapCityToCode(components.locality, stateCode);
        
        return {
            address: address1.trim() || components.formattedAddress.split(',')[0],
            address2: address2 || null,
            state: stateCode,
            city: cityCode,
            postcode: components.postalCode || this.generatePostcode(stateCode)
        };
    }
    
    /**
     * 映射州名到代码
     */
    mapStateToCode(stateName) {
        const stateMapping = {
            'Johor': '01', 'Kedah': '02', 'Kelantan': '03', 'Melaka': '04',
            'Negeri Sembilan': '05', 'Pahang': '06', 'Penang': '07', 'Perak': '08',
            'Perlis': '09', 'Sabah': '10', 'Sarawak': '11', 'Selangor': '12',
            'Terengganu': '13', 'Kuala Lumpur': '14', 'Labuan': '15', 'Putrajaya': '16'
        };
        
        return stateMapping[stateName] || '14'; // 默认吉隆坡
    }
    
    /**
     * 映射城市名到代码
     */
    mapCityToCode(cityName, stateCode) {
        const cityMapping = {
            'Kuala Lumpur': '1400', 'George Town': '1000', 'Melaka': '0700',
            'Johor Bahru': '0118', 'Johor': '0100', 'Shah Alam': '1200', 'Ipoh': '0800',
            'Kota Kinabalu': '1000', 'Kuching': '1100',
            // 新山相关的映射
            '新山': '0118', '新山乐高': '0118', 'Legoland': '0118',
            'Iskandar Puteri': '0196', 'Senai': '0144', 'Kulai': '0124',
            'Masai': '0127', 'Skudai': '0142', 'Pasir Gudang': '0136'
        };
        
        // 首先尝试直接匹配
        if (cityMapping[cityName]) {
            return cityMapping[cityName];
        }
        
        // 尝试忽略大小写的匹配
        const lowerCityName = cityName.toLowerCase();
        for (const [key, value] of Object.entries(cityMapping)) {
            if (key.toLowerCase() === lowerCityName) {
                return value;
            }
        }
        
        // 如果包含关键词，也进行匹配
        if (lowerCityName.includes('johor bahru') || lowerCityName.includes('新山') || 
            lowerCityName.includes('legoland') || lowerCityName.includes('乐高')) {
            return '0118';
        }
        
        return (stateCode + '00');
    }
    
    /**
     * 生成邮政编码（如果缺失）
     */
    generatePostcode(stateCode) {
        const range = this.statePostcodeRanges[stateCode];
        if (range) {
            // 返回范围内的中间值
            const midpoint = Math.floor((range[0] + range[1]) / 2);
            return midpoint.toString();
        }
        return '50000'; // 默认吉隆坡邮编
    }
    
    /**
     * 评估地址质量
     */
    assessAddressQuality(components, validation) {
        let score = 0;
        const factors = [];
        
        // 完整性评分
        if (components.streetNumber) { score += 0.15; factors.push('有门牌号'); }
        if (components.streetName) { score += 0.25; factors.push('有街道名'); }
        if (components.locality) { score += 0.20; factors.push('有城市名'); }
        if (components.administrativeArea1) { score += 0.20; factors.push('有州名'); }
        if (components.postalCode) { score += 0.20; factors.push('有邮编'); }
        
        // 位置准确性
        score *= validation.confidence;
        
        return {
            score: Math.min(score, 1.0),
            grade: score >= 0.9 ? 'A' : score >= 0.7 ? 'B' : score >= 0.5 ? 'C' : 'D',
            factors: factors,
            completeness: factors.length / 5,
            accuracy: validation.confidence,
            isValid: score >= 0.5 && validation.inMalaysia
        };
    }
    
    /**
     * 生成改进建议
     */
    generateSuggestions(components, quality) {
        const suggestions = [];
        
        if (!components.streetNumber) {
            suggestions.push('建议添加门牌号码以提高地址精确度');
        }
        
        if (!components.postalCode) {
            suggestions.push('建议添加邮政编码');
        }
        
        if (quality.score < 0.7) {
            suggestions.push('地址信息不够完整，建议提供更详细的地址');
        }
        
        if (!components.locality) {
            suggestions.push('建议明确指定城市名称');
        }
        
        return suggestions;
    }
    
    /**
     * 格式化标准地址
     */
    formatStandardAddress(components) {
        const parts = [];
        
        if (components.streetNumber && components.streetName) {
            parts.push(`${components.streetNumber} ${components.streetName}`);
        } else if (components.streetName) {
            parts.push(components.streetName);
        }
        
        if (components.sublocality) {
            parts.push(components.sublocality);
        }
        
        if (components.locality) {
            parts.push(components.locality);
        }
        
        if (components.postalCode && components.administrativeArea1) {
            parts.push(`${components.postalCode} ${components.administrativeArea1}`);
        }
        
        if (components.country) {
            parts.push(components.country);
        }
        
        return parts.join(', ');
    }
    
    /**
     * 清理地址字符串
     */
    cleanAddress(address) {
        return address
            .trim()
            .replace(/[，。]/g, ',') // 中文标点转英文
            .replace(/\s+/g, ' ') // 多个空格合并
            .replace(/^[,\s]+|[,\s]+$/g, ''); // 移除首尾逗号和空格
    }
    
    /**
     * 缓存管理
     */
    getFromCache(key) {
        const item = this.cache.get(key);
        if (item && Date.now() - item.timestamp < this.cacheConfig.ttl) {
            return item.data;
        }
        this.cache.delete(key);
        return null;
    }
    
    setCache(key, data) {
        if (this.cache.size >= this.cacheConfig.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        
        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }
    
    /**
     * 更新平均响应时间
     */
    updateAverageResponseTime(responseTime) {
        const total = this.stats.averageResponseTime * (this.stats.successfulRequests - 1) + responseTime;
        this.stats.averageResponseTime = Math.round(total / this.stats.successfulRequests);
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.stats,
            successRate: this.stats.totalRequests > 0 ? 
                (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2) + '%' : '0%',
            cacheHitRate: this.stats.totalRequests > 0 ? 
                (this.stats.cacheHits / this.stats.totalRequests * 100).toFixed(2) + '%' : '0%'
        };
    }
    
    /**
     * 批量地址标准化
     */
    async batchStandardize(addresses, options = {}) {
        const results = [];
        const delay = options.delay || this.config.rateLimitDelay;
        
        for (let i = 0; i < addresses.length; i++) {
            const result = await this.standardizeAddress(addresses[i], options);
            results.push(result);
            
            // API调用间隔
            if (i < addresses.length - 1) {
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        
        return results;
    }
}

// 导出类
if (typeof window !== 'undefined') {
    window.GoogleMapsIntegration = GoogleMapsIntegration;
} else if (typeof module !== 'undefined' && module.exports) {
    module.exports = GoogleMapsIntegration;
}
