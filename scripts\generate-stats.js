const fs = require('fs');

// 读取马来西亚数据
const data = JSON.parse(fs.readFileSync('config/malaysia-states-cities.json', 'utf8'));

console.log('🇲🇾 马来西亚州属城市数据统计报告');
console.log('='.repeat(50));

// 统计州属
const stateCount = Object.keys(data.states).length;
console.log(`📍 州属总数: ${stateCount}`);

// 统计城市
let totalCities = 0;
let stateStats = [];

Object.entries(data.cities).forEach(([stateCode, stateInfo]) => {
  const cityCount = Object.keys(stateInfo.cities).length;
  totalCities += cityCount;
  stateStats.push({
    code: stateCode,
    name: data.states[stateCode],
    cityCount: cityCount
  });
});

console.log(`🏙️ 城市总数: ${totalCities}`);

// 热门目的地统计
const popularCount = data.popularDestinations.tourist_cities.length;
console.log(`🎯 热门目的地: ${popularCount}`);

console.log('\n📊 各州属城市分布:');
stateStats.sort((a, b) => b.cityCount - a.cityCount);
stateStats.forEach((state, index) => {
  console.log(`${(index + 1).toString().padStart(2)}. ${state.code} - ${state.name.padEnd(20)} : ${state.cityCount.toString().padStart(2)} 个城市`);
});

console.log('\n🎯 热门旅游目的地:');
data.popularDestinations.tourist_cities.forEach((dest, index) => {
  console.log(`${(index + 1).toString().padStart(2)}. ${dest.name} (${dest.chinese}) - ${dest.reason}`);
});

console.log('\n✅ 数据完整性检查:');
let issues = 0;
Object.entries(data.cities).forEach(([stateCode, stateInfo]) => {
  Object.entries(stateInfo.cities).forEach(([cityCode, cityInfo]) => {
    if (!cityInfo.name) issues++;
    if (!cityInfo.chinese) issues++;
    if (!cityInfo.postcode) issues++;
  });
});

if (issues === 0) {
  console.log('✅ 所有数据完整');
} else {
  console.log(`❌ 发现 ${issues} 个数据缺失项`);
}

console.log('\n📋 数据库概览:');
console.log(`• 覆盖马来西亚全部 ${stateCount} 个州属/联邦直辖区`);
console.log(`• 包含 ${totalCities} 个主要城市和地区`);
console.log(`• 提供完整的中英文对照`);
console.log(`• 包含邮政编码范围映射`);
console.log(`• 标记 ${popularCount} 个热门旅游目的地`);
console.log(`• 支持智能模糊匹配算法`);
