# 🚀 马来西亚州属城市数据库 - 快速开始

## 📥 安装和集成

### 1. 文件引用
```javascript
// 在Chrome扩展中使用
const response = await fetch(chrome.runtime.getURL('config/malaysia-states-cities.json'));
const data = await response.json();

// 在网页中使用
const response = await fetch('config/malaysia-states-cities.json');
const data = await response.json();
```

### 2. 初始化验证器
```javascript
const validator = new MDACValidator();
await validator.loadCodeMappings();
```

## 🔧 基础用法

### 州属匹配
```javascript
// 各种输入格式都支持
validator.smartStateMapping('柔佛');        // → '01'
validator.smartStateMapping('Johor');       // → '01'
validator.smartStateMapping('吉隆坡');      // → '14'
validator.smartStateMapping('KL');          // → '14'
```

### 城市匹配
```javascript
// 支持中英文城市名
validator.smartCityMapping('新山');         // → '0100'
validator.smartCityMapping('Johor Bahru');  // → '0100'
validator.smartCityMapping('乔治市');       // → '0700'
validator.smartCityMapping('George Town');  // → '0700'

// 指定州属提高准确性
validator.smartCityMapping('槟城', '07');   // → '0700'
```

### 邮政编码匹配
```javascript
validator.getCityByPostcode('50000');  // → '1400' (吉隆坡)
validator.getCityByPostcode('81300');  // → '0142' (士古来)
validator.getCityByPostcode('10000');  // → '0700' (乔治市)
```

## 🎯 高级功能

### 获取州属城市列表
```javascript
const cities = validator.getCitiesByState('01');  // 获取柔佛州所有城市
cities.forEach(city => {
    console.log(`${city.code} - ${city.name} (${city.chinese})`);
});
```

### 验证城市州属匹配
```javascript
const isValid = validator.validateCityStateMatch('0100', '01');  // true
const isInvalid = validator.validateCityStateMatch('0100', '07'); // false
```

### 获取热门目的地
```javascript
const popular = validator.getPopularDestinations();
popular.forEach(dest => {
    console.log(`${dest.name} (${dest.chinese}) - ${dest.reason}`);
});
```

## 📊 数据统计

- **16个州属** - 完整覆盖马来西亚所有州属
- **237个城市** - 包含主要城市和旅游目的地
- **10个热门目的地** - 特别标记的旅游城市
- **双语支持** - 完整的中英文对照
- **邮编映射** - 准确的邮政编码范围

## 🛠️ 实际应用示例

### 表单自动填充
```javascript
class FormFiller {
    async fillLocation(userInput) {
        // 处理用户输入的地址信息
        const stateCode = this.validator.smartStateMapping(userInput.state);
        const cityCode = this.validator.smartCityMapping(userInput.city, stateCode);
        
        // 验证匹配
        if (this.validator.validateCityStateMatch(cityCode, stateCode)) {
            return { stateCode, cityCode, valid: true };
        }
        
        return { valid: false, error: '城市与州属不匹配' };
    }
}
```

### 地址验证
```javascript
function validateAddress(address, postcode) {
    const cityFromPostcode = validator.getCityByPostcode(postcode);
    if (!cityFromPostcode) {
        return { valid: false, error: '无效的邮政编码' };
    }
    
    // 可以进一步验证地址是否在正确的城市
    return { valid: true, cityCode: cityFromPostcode };
}
```

## 🧪 测试你的集成

### 1. 基础测试
```javascript
// 测试州属匹配
console.assert(validator.smartStateMapping('柔佛') === '01');
console.assert(validator.smartStateMapping('Johor') === '01');

// 测试城市匹配
console.assert(validator.smartCityMapping('新山') === '0100');
console.assert(validator.smartCityMapping('Johor Bahru') === '0100');

// 测试邮编匹配
console.assert(validator.getCityByPostcode('50000') === '1400');
```

### 2. 完整测试
打开 `test/malaysia-data-test.html` 进行交互式测试。

### 3. 运行统计
```bash
node scripts/generate-stats.js
```

## 📝 最佳实践

### 1. 错误处理
```javascript
try {
    const stateCode = validator.smartStateMapping(userInput);
    if (!stateCode) {
        console.warn('无法匹配州属:', userInput);
        // 提供用户选择或建议
    }
} catch (error) {
    console.error('州属匹配错误:', error);
}
```

### 2. 性能优化
```javascript
// 缓存常用匹配结果
const cache = new Map();

function cachedStateMapping(input) {
    if (cache.has(input)) {
        return cache.get(input);
    }
    
    const result = validator.smartStateMapping(input);
    cache.set(input, result);
    return result;
}
```

### 3. 用户体验
```javascript
// 提供智能建议
function suggestCorrections(input) {
    const directMatch = validator.smartCityMapping(input);
    if (directMatch) return [directMatch];
    
    // 提供可能的匹配选项
    const suggestions = [];
    // 实现模糊搜索逻辑...
    return suggestions;
}
```

## 🔗 相关文件

- **数据文件**: `config/malaysia-states-cities.json`
- **验证器**: `utils/mdac-validator.js`
- **表单填充器**: `utils/enhanced-form-filler.js`
- **测试页面**: `test/malaysia-data-test.html`
- **使用示例**: `examples/malaysia-data-usage.js`
- **详细文档**: `docs/MALAYSIA_STATES_CITIES_DATABASE.md`

## 📞 支持

如果遇到问题或需要添加新的城市数据，请：

1. 检查现有文档和测试页面
2. 查看使用示例代码
3. 运行数据完整性验证
4. 提交Issue或Pull Request

---

**快速上手完成！** 🎉 现在你可以开始使用完整的马来西亚州属城市数据库了。
